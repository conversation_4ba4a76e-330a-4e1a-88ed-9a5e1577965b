import React from "react";
import Button from "@components/Button";
import { AddressTypeSelector } from "./AddressTypeSelector";
import { AddressType } from "~/types/address.types";
import { cn } from "~/utils/cn";
import BottomSheet from "../BottmSheet";

interface AddressFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  autoCompleteAddress: string;
  address: string;
  setAddress: (value: string) => void;
  nearbyLandmarkAddress: string;
  setNearbyLandmarkAddress: (value: string) => void;
  addressType: AddressType;
  setAddressType: (type: AddressType) => void;
  customAddressLabel: string;
  setCustomAddressLabel: (label: string) => void;
  errors?: {
    address?: string;
    customLabel?: string;
    userName?: string;
  };
  /**
   * Whether to show the name field in the form
   */
  showNameField?: boolean;
  /**
   * Whether the name field should be disabled
   */
  isNameFieldDisabled?: boolean;
  /**
   * Current user name value
   */
  userName?: string;
  /**
   * Setter for user name
   */
  setUserName?: (name: string) => void;
}

/**
 * Modal component for entering and submitting address details
 */
export const AddressFormModal: React.FC<AddressFormModalProps> = ({
  isOpen,
  onClose,
  autoCompleteAddress,
  address = "",
  setAddress,
  nearbyLandmarkAddress,
  setNearbyLandmarkAddress,
  addressType,
  setAddressType,
  customAddressLabel,
  setCustomAddressLabel,
  errors,
  showNameField = false,
  isNameFieldDisabled = false,
  userName = "",
  setUserName = () => {}
}) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const [isNearbyFocused, setIsNearbyFocused] = React.useState(false);
  const [isNameFocused, setIsNameFocused] = React.useState(false);

  // if (!isOpen) return null;

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={onClose}
      className="w-full max-h-[80vh]"
      backdropClassName="bg-black bg-opacity-50"
      showSwipeIndicator={false}
      // sheetType="drawer"
    >
      <div className="relative flex flex-col gap-2 bg-neutral-200 rounded-t-xl">
        {/* header */}
        <div className="px-3 pt-3">
          <label
            htmlFor="address"
            className="block font-semibold text-lg text-gray-500"
          >
            Enter complete address
          </label>
        </div>

        <div className="px-3 bg-neutral-200">
          <div className="border-t border-gray-300"></div>
        </div>

        {/* User Name field (shown conditionally) */}
        {showNameField && (
          <div className="px-3 pt-5 pb-4 mt-1 mx-3 flex flex-col gap-2 align-bottom rounded-xl bg-white">
            <div className="w-full transition-all duration-1000 ease-in-out">
              <div className="relative transition-all duration-1000 ease-in-out">
                <input
                  type="text"
                  name="userName"
                  id="userName"
                  value={userName}
                  onChange={(e) => {
                    setUserName(e.target.value);
                  }}
                  onFocus={() => setIsNameFocused(true)}
                  onBlur={() => setIsNameFocused(false)}
                  required
                  disabled={isNameFieldDisabled}
                  placeholder={!isNameFocused ? "Receiver's Name *" : ""}
                  className={cn(
                    "w-full px-5 py-3 border bg-transparent rounded-xl focus:outline-none focus:border-neutral-700 text-typography-800 text-sm",
                    errors?.userName ? "border-red-500" : "border-neutral-600",
                    isNameFieldDisabled
                      ? " cursor-not-allowed text-gray-400 "
                      : ""
                  )}
                />
                {(userName.length > 0 || isNameFocused) && (
                  <span
                    className={`transition-all duration-300 ease-linear absolute top-[-8px] left-4 bg-white px-1 text-sm rounded-md
                      ${
                        isNameFocused ? "text-neutral-900" : "text-neutral-700"
                      }`}
                  >
                    Receiver&apos;s Name *
                  </span>
                )}
              </div>
            </div>
            {errors?.userName ? (
              <p className="text-red-500 text-sm ml-1">{errors.userName}</p>
            ) : (
              userName.length === 0 && (
                <p className="text-red-500 text-xs ml-1">
                  * Please enter a name
                </p>
              )
            )}
          </div>
        )}

        <div className="flex flex-col gap-5 px-3 py-4 mt-1 mx-3 rounded-xl bg-white">
          {/* Address Type Selector */}
          <div className="flex flex-col gap-2">
            <div className="block text-sm font-medium text-typography-700">
              Save address as *
            </div>
            <AddressTypeSelector
              selectedType={addressType}
              onChange={setAddressType}
              variant="buttons"
              customLabel={customAddressLabel}
              onCustomLabelChange={setCustomAddressLabel}
            />
          </div>
          {/* Auto complete address */}
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center gap-2 w-full border border-neutral-400 p-3 rounded-xl bg-neutral-100">
              <span className="text-sm text-typography-700 overflow-hidden">
                {autoCompleteAddress}
              </span>
              <button
                onClick={onClose}
                className="px-3 py-1 text-primary border border-primary rounded-lg text-xs bg-white hover:bg-primary-100 focus:outline-none"
              >
                Change
              </button>
            </div>
            <p className="text-xs text-gray-500">
              Updated based on your exact map pin
            </p>
          </div>
          {/* Address input */}
          <div className="flex flex-col gap-2 align-bottom mt-2">
            <div className="w-full transition-all duration-1000 ease-in-out">
              <div className="relative transition-all duration-1000 ease-in-out">
                <input
                  type="text"
                  name="address"
                  id="address"
                  value={address}
                  onChange={(e) => {
                    setAddress(e.target.value);
                  }}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  required
                  placeholder={
                    !isFocused ? "Flat / House no / Floor / Building *" : ""
                  }
                  className={`w-full px-5 py-3 border bg-transparent ${
                    errors?.address ? "border-red-500" : "border-neutral-600"
                  } 
                  rounded-xl focus:outline-none focus:border-neutral-700 text-typography-800`}
                />
                {(address.length > 0 || isFocused) && (
                  <span
                    className={`transition-all duration-300 ease-linear absolute top-[-8px] left-4 bg-white px-1 text-sm rounded-md
                            ${
                              isFocused
                                ? "text-neutral-900"
                                : "text-neutral-700"
                            }`}
                  >
                    Flat / House no / Floor / Building *
                  </span>
                )}
              </div>
            </div>
            {address.length === 0 && (
              <p className="text-red-500 text-xs ml-1">
                * Please enter an address
              </p>
            )}
          </div>
          {/* Nearby Landmark address */}
          <div className="flex flex-col gap-2 align-bottom">
            <div className="w-full transition-all duration-1000 ease-in-out">
              <div className="relative transition-all duration-1000 ease-in-out">
                <input
                  type="text"
                  name="nearbyLandmark"
                  id="nearbyLandmark"
                  value={nearbyLandmarkAddress}
                  onChange={(e) => setNearbyLandmarkAddress(e.target.value)}
                  onFocus={() => setIsNearbyFocused(true)}
                  onBlur={() => setIsNearbyFocused(false)}
                  placeholder={
                    !isNearbyFocused ? "Nearby Landmark (optional)" : ""
                  }
                  className={`w-full px-5 py-3 border bg-transparent ${
                    errors?.address ? "border-red-500" : "border-neutral-600"
                  }
                  rounded-xl focus:outline-none focus:border-neutral-700 text-typography-800`}
                />
                {(nearbyLandmarkAddress?.length > 0 || isNearbyFocused) && (
                  <span
                    className={`transition-all duration-300 ease-linear absolute top-[-8px] left-4 bg-white px-1 text-sm rounded-md
                            ${
                              isNearbyFocused
                                ? "text-neutral-900"
                                : "text-neutral-700"
                            }`}
                  >
                    {`Nearby Landmark (optional)`}
                  </span>
                )}
              </div>
            </div>
          </div>
          <input
            hidden={true}
            name="autoCompleteAddress"
            id="autoCompleteAddress"
            value={autoCompleteAddress}
          />
          {errors?.address && (
            <p className="text-red-500 text-sm mt-1">{errors.address}</p>
          )}
          {errors?.customLabel && (
            <p className="text-red-500 text-xs mt-1">{errors.customLabel}</p>
          )}
        </div>
        {/* Submit Button */}
        <div className="sticky bottom-0 left-0 w-full bg-white p-3 mt-4 rounded-t-lg">
          <Button
            disabled={
              address.length === 0 || (showNameField && userName.length === 0)
            }
            type="submit"
            className={`px-4 py-2 rounded-md w-full ${
              address.length === 0 || (showNameField && userName.length === 0)
                ? "bg-neutral-400 text-typography-200 !opacity-100"
                : "bg-primary text-white"
            }`}
          >
            Save Address
          </Button>
        </div>
      </div>
    </BottomSheet>
  );
};
