import { ItemsVariantList } from "../ItemsVariantList";
import React, { useCallback, useRef, useState, useMemo } from "react";
import { AvailableItem, Cart, CustomizationItem } from "~/types";
import Button from "../Button";
import BottomSheet from "../BottmSheet";
import CustomImage from "../CustomImage";
import AddItemButtonV2 from "../chooseitem/AddItemButtonV2";
import ItemCustomization from "./ItemCustomization";
import { extractTags, COMMON_TAGS } from "~/utils/menuUtils";
import { cn } from "~/utils/cn";
import { isEmptyNullOrUndefinedString } from "~/utils/string";
import ItemStockOutButton from "../common/ItemStockOutButton";
import { DietaryImage } from "../common/DietaryImage";
import { formatCurrency } from "~/utils/format";
import { PlusIcon } from "lucide-react";
import { useCartStore } from "~/stores/cart.store";

// const mockAddonGroups: AogList[] = [
//   {
//     id: 1,
//     name: "Customisations",
//     type: "customization",
//     isMandatory: true,
//     selectionType: "single",
//     minSelection: 1,
//     maxSelection: 1,
//     items: [
//       {
//         id: "chicken",
//         name: "Chicken",
//         price: 180,
//         inStock: true
//       },
//       {
//         id: "lamb",
//         name: "Lamb",
//         price: 280,
//         inStock: true
//       },
//       {
//         id: "tenderloin",
//         name: "Tenderloin (Buff)",
//         price: 330,
//         inStock: true,
//         isDefault: true
//       }
//     ]
//   },
//   {
//     id: 2,
//     name: "Add-ons",
//     type: "addOn",
//     isMandatory: false,
//     selectionType: "multiple",
//     minSelection: 0,
//     maxSelection: 8,
//     items: [
//       {
//         id: "onions",
//         name: "Caramelised Onions",
//         price: 10,
//         inStock: true,
//         isDefault: true
//       },
//       {
//         id: "cheese",
//         name: "Cheese Slice",
//         price: 15,
//         inStock: true,
//         isDefault: true
//       },
//       {
//         id: "lettuce",
//         name: "Lettuce",
//         price: 25,
//         inStock: true,
//         isDefault: true
//       },
//       {
//         id: "bacon",
//         name: "Bacon",
//         price: 90,
//         inStock: true
//       }
//     ]
//   },
//   {
//     id: 3,
//     name: "Extra",
//     type: "extra",
//     isMandatory: false,
//     selectionType: "item-qty",
//     minSelection: 0,
//     maxSelection: 8,
//     items: [
//       {
//         id: "extra-cheese",
//         name: "Extra Cheese",
//         price: 40,
//         inStock: true
//       },
//       {
//         id: "extra-sauce",
//         name: "Special Sauce",
//         price: 25,
//         inStock: true
//       },
//       {
//         id: "jalapenos",
//         name: "Jalapeños",
//         price: 30,
//         inStock: true
//       },
//       {
//         id: "guacamole",
//         name: "Guacamole",
//         price: 50,
//         inStock: true
//       }
//     ]
//   }
// ];

interface ItemCardProps {
  itemDetailsList: AvailableItem[];
  amount: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  cart: Cart;
  isLast?: boolean;
  onAddonsUpdate?: () => void;
}

const ItemCard: React.FC<ItemCardProps> = ({
  itemDetailsList,
  amount,
  cart,
  onAdd,
  onRemove,
  approxPricing,
  isLast = false,
  onAddonsUpdate
}) => {
  const [showItemDetails, setShowItemDetails] = useState(false);
  const [showVariants, setShowVariants] = useState(false);
  const [showMultiCustomization, setShowMultiCustomization] = useState(false);
  const { increaseCustomizationQty, decreaseCustomizationQty } = useCartStore();
  const [selectedCustomItem, setSelectedCustomItem] =
    useState<CustomizationItem | null>(null);

  const sortedItems = useCallback(
    () =>
      [...itemDetailsList]
        .sort((a, b) => a.varSeq - b.varSeq)
        .sort((a, b) => {
          if ((a.soldout || a.closed) && !(b.soldout || b.closed)) return 1;
          if (!(a.soldout || a.closed) && (b.soldout || b.closed)) return -1;
          return a.varSeq - b.varSeq;
        }),
    [itemDetailsList]
  )();

  const itemDetails = useCallback(() => {
    const item = [...itemDetailsList]
      .sort((a, b) => a.varSeq - b.varSeq)
      .find((item) => !(item.soldout || item.closed));

    return item || itemDetailsList[0];
  }, [itemDetailsList])();

  const qty = useCallback(() => {
    return itemDetailsList.reduce(
      (acc, it) => acc + (cart[it.sellerItemId]?.qty || 0),
      0
    );
  }, [{ ...cart }])();

  const cartItem = cart[itemDetails.sellerItemId];

  const haslessdata = !(itemDetails.itemName && itemDetails.packaging);

  const bottomSheetRef = useRef<HTMLDivElement | null>(null);

  const handleSelectedCustomItem = (customItem: CustomizationItem) => {
    setSelectedCustomItem(customItem);
    setShowMultiCustomization(false);
    setShowItemDetails(true);
  };

  return (
    <div
      className={`flex flex-col gap-2 relative w-full p-2 ${!isLast ? "" : ""}`}
    >
      {haslessdata && (
        <ItemInfo
          cart={cart}
          itemDetails={itemDetails}
          showItemDetails={showItemDetails}
          setShowItemDetails={setShowItemDetails}
          showMultiCustomization={showMultiCustomization}
          setShowMultiCustomization={setShowMultiCustomization}
          amount={amount}
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          haslessdata={haslessdata}
          variantCount={itemDetailsList?.length || 0}
          setShowVariants={setShowVariants}
        />
      )}
      {!haslessdata && (
        <>
          <ItemInfo
            cart={cart}
            itemDetails={itemDetails}
            showItemDetails={showItemDetails}
            setShowItemDetails={setShowItemDetails}
            showMultiCustomization={showMultiCustomization}
            setShowMultiCustomization={setShowMultiCustomization}
            amount={amount}
            qty={qty}
            onAdd={onAdd}
            onRemove={onRemove}
            approxPricing={approxPricing}
            haslessdata={haslessdata}
            variantCount={itemDetailsList?.length || 0}
            setShowVariants={setShowVariants}
          />
        </>
      )}

      <ItemsVariantList
        showVariants={showVariants}
        setShowVariants={setShowVariants}
        variants={sortedItems}
        cart={cart}
        onAdd={onAdd}
        onRemove={onRemove}
      />

      <BottomSheet
        isOpen={showItemDetails}
        onClose={() => setShowItemDetails(false)}
        className="bg-gray-100"
        backdropClassName="z-[50]"
        ref={bottomSheetRef}
        sheetType="drawer"
        swipeIndicatorClassName="bg-gray-100 before:bg-gray-300"
      >
        {itemDetails && (
          <ItemCustomization
            item={itemDetails}
            cart={cart}
            onAdd={() => setShowItemDetails(false)}
            onRemove={onRemove}
            approxPricing={approxPricing}
            setShowItemDetails={setShowItemDetails}
            onAddonsUpdate={onAddonsUpdate}
            parentRef={bottomSheetRef}
            customItemKey={selectedCustomItem?.key}
          />
        )}
      </BottomSheet>

      <BottomSheet
        isOpen={showMultiCustomization}
        onClose={() => setShowMultiCustomization(false)}
        sheetType="drawer"
      >
        <div>
          <p className="bg-white p-3 font-semibold text-typography-800 mb-1">
            Repeat last used customization?
          </p>

          {cartItem?.customizationList &&
            cartItem?.customizationList.map((ctm, index) => (
              <div
                key={index}
                className="flex justify-between items-start gap-6 p-2.5"
              >
                <div className="flex flex-row gap-2">
                  <div className="pt-0.5">
                    <DietaryImage
                      dietary={itemDetails.diet}
                      className="min-w-4 min-h-4"
                    />
                  </div>
                  <div>
                    <div className="flex flex-col gap-0.5">
                      <h3
                        className={`text-sm font-medium overflow-hidden text-typography-900 line-clamp-2`}
                      >
                        {itemDetails.itemName}
                      </h3>
                    </div>
                    {(ctm.flatAddons || ctm.variationId) && (
                      <p className="text-xs font-normal text-typography-400 line-clamp-2">
                        {ctm.variationId
                          ? itemDetails.itemVariationList?.filter(
                              (v) => v.id === ctm.variationId
                            )[0]?.name + (ctm.flatAddons?.length ? ", " : "")
                          : ""}
                        {ctm.flatAddons
                          ?.reduce((acc, item) => {
                            return acc + item.name + ", ";
                          }, "")
                          .slice(0, -2)}
                      </p>
                    )}
                    {
                      <Button
                        className="flex flex-row items-center gap-1 text-[13px] font-semibold text-typography-400 mt-1 cursor-pointer"
                        onClick={() => handleSelectedCustomItem(ctm)}
                      >
                        <p>Edit</p>
                        <img
                          src="/play.svg"
                          alt="Right"
                          className="w-2.5 h-2.5"
                        />
                      </Button>
                    }
                  </div>
                </div>
                {
                  <div className="flex flex-col items-end gap-2">
                    {/* <p className="text-sm font-thin text-typography-800 pr-2"> */}

                    {
                      <AddItemButtonV2
                        qty={ctm?.qty || 0}
                        onAdd={() => {
                          increaseCustomizationQty(
                            itemDetails,
                            cartItem.cartKey,
                            ctm.key
                          );
                        }}
                        onRemove={() => {
                          decreaseCustomizationQty(
                            itemDetails,
                            cartItem.cartKey,
                            ctm.key
                          );
                        }}
                        isDisabled={false}
                        unit={itemDetails.unit}
                        btnConfig={{
                          showUnit: false,
                          iconSize: 16,
                          btnType: "secondary"
                        }}
                        className="h-8 text-xs w-[74px] bg-teal-50 rounded-[.25rem]"
                      />
                    }
                    <div className="flex flex-row items-baseline gap-1">
                      <p className="text-[10px] font-light text-typography-600">
                        {formatCurrency(ctm?.amount || 0)}
                      </p>
                    </div>
                  </div>
                }
              </div>
            ))}

          <Button
            className="w-full flex items-center gap-3 justify-center p-4 text-primary font-bold"
            onClick={() => {
              setSelectedCustomItem(null);
              setShowMultiCustomization(false);
              setShowItemDetails(true);
            }}
          >
            <PlusIcon /> Add new customization
          </Button>
        </div>
      </BottomSheet>
    </div>
  );
};

// Render star rating
const renderRating = (showRating: boolean, rating: number) => {
  if (!showRating) return null;

  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <svg
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? "text-yellow-400" : "text-gray-300"
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  );
};

function getDiscountPercentage(
  previousPrice: number,
  currentPrice: number
): string {
  if (previousPrice <= 0 || currentPrice > previousPrice)
    return "Invalid prices";

  const discount = ((previousPrice - currentPrice) / previousPrice) * 100;
  return `${Math.round(discount)} % OFF`;
}

// Render a single badge based on type
const renderSingleBadge = (badgeType: string) => {
  switch (badgeType.toLowerCase()) {
    case COMMON_TAGS.BESTSELLER.toLowerCase():
      return (
        <div className="bg-orange-50 text-orange-500 text-xs font-semibold py-0.5 px-1 rounded">
          <span>Bestseller</span>
        </div>
      );
    case COMMON_TAGS.SPICY.toLowerCase():
      return (
        <div className="text-red-800 text-xs rounded">
          <img
            src="/spicy-icon.svg"
            alt="Spicy"
            className="w-4 h-4 min-w-4 min-h-4 inline"
          />
        </div>
      );
    case COMMON_TAGS.VEG.toLowerCase():
      return (
        <div className="text-green-800 text-xs rounded">
          <img
            src="/veg-icon.svg"
            alt="veg"
            className="w-4 h-4 min-w-4 min-h-4 inline"
          />
          {/* <span>Veg</span> */}
        </div>
      );
    case COMMON_TAGS.NON_VEG.toLowerCase():
      return (
        <div className="text-red-800 text-xs rounded">
          <img
            src="/non-veg-icon.svg"
            alt="non-veg"
            className="w-4 h-4 min-w-4 min-h-4 inline"
          />
          {/* <span>Non-Veg</span> */}
        </div>
      );
    case COMMON_TAGS.EGG.toLowerCase():
      return (
        <div className="text-yellow-700 text-xs rounded">
          <img
            src="/egg-icon.svg"
            alt="egg"
            className="w-4 h-4 min-w-4 min-h-4 inline"
          />
          {/* <span>Contains Egg</span> */}
        </div>
      );
    default:
      return null;
  }
};

// Render multiple badges using the extractTags utility
const renderBadges = (item: AvailableItem) => {
  // Extract all tags from the item
  const tags = extractTags(item);

  if (tags.size === 0) return null;

  // Priority order for display
  const priorityTags = [
    COMMON_TAGS.VEG.toLowerCase(),
    COMMON_TAGS.NON_VEG.toLowerCase(),
    COMMON_TAGS.EGG.toLowerCase(),
    COMMON_TAGS.BESTSELLER.toLowerCase(),
    COMMON_TAGS.SPICY.toLowerCase()
  ];

  // Sort tags by priority
  const sortedTags = Array.from(tags)
    .map((tag) => tag.toLowerCase())
    .sort((a, b) => {
      const indexA = priorityTags.indexOf(a);
      const indexB = priorityTags.indexOf(b);

      if (indexA !== -1 && indexB !== -1) return indexA - indexB;
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;
      return a.localeCompare(b);
    });

  return (
    <div className="flex flex-row gap-1 w-fit flex-wrap">
      {sortedTags.map((tag, index) => (
        <React.Fragment key={index}>{renderSingleBadge(tag)}</React.Fragment>
      ))}
    </div>
  );
};

// Component for displaying item image, name, and badges
const ItemInfo: React.FC<{
  cart: Cart;
  itemDetails: AvailableItem;
  showItemDetails: boolean;
  setShowItemDetails: (value: boolean) => void;
  showMultiCustomization: boolean;
  setShowMultiCustomization: (value: boolean) => void;
  amount: number;
  qty: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  haslessdata: boolean;
  variantCount: number;
  setShowVariants: (show: boolean) => void;
}> = ({
  cart,
  itemDetails,
  showItemDetails,
  setShowItemDetails,
  showMultiCustomization,
  setShowMultiCustomization,
  qty,
  onAdd,
  onRemove
}) => {
  const hasCustomizations =
    (itemDetails.aogList && itemDetails.aogList.length > 0) ||
    (itemDetails.itemVariationList && itemDetails.itemVariationList.length > 0);

  const itemPrices = useMemo(() => {
    // if variants are present, return the price of the minimum variant price
    if (
      itemDetails.itemVariationList &&
      itemDetails.itemVariationList.length > 0
    ) {
      const minVariant = itemDetails.itemVariationList.reduce((min, current) =>
        current.price < min.price ? current : min
      );
      return {
        price: minVariant.price,
        strikeoffPrice: minVariant.strikeoffPrice
      };
    }

    return {
      price: itemDetails.pricePerUnit,
      strikeoffPrice: itemDetails.strikeoffPrice
    };
  }, [
    itemDetails.itemVariationList,
    itemDetails.pricePerUnit,
    itemDetails.strikeoffPrice
  ]);

  return (
    <div className="flex flex-row items-start justify-between w-full gap-3 flex-nowrap">
      <div className="flex flex-col justify-between min-w-0 flex-1 gap-1">
        {/* Badge for categories */}
        <div className="">{renderBadges(itemDetails)}</div>
        <div className="flex flex-grow flex-col items-start text-start  w-full">
          {/* Product name and details */}
          <div className="flex flex-grow flex-col items-start text-start w-full gap-2">
            <Button
              onClick={() => setShowItemDetails(!showItemDetails)}
              className="text-md font-semibold text-typography-800 line-clamp-2 text-left"
              disabled={itemDetails.closed || itemDetails.soldout}
            >
              {itemDetails.itemName}
            </Button>
            {/* Rating stars if applicable - using a safe fallback */}
            {renderRating(false, 0)}

            <div className="flex flex-row items-center justify-center gap-2">
              <p
                className={`text-sm ${
                  itemDetails.closed || itemDetails.soldout
                    ? "text-typography-200"
                    : "text-typography-500"
                } font-medium`}
              >
                ₹<span className="text-sm ">{itemPrices.price}</span>
              </p>
              {itemDetails.discPerc > 0 && (
                <span className="text-xs font-light text-neutral-800 line-through">
                  ₹ {itemPrices.strikeoffPrice}
                </span>
              )}
            </div>
            {itemDetails.discPerc > 0 && (
              <span className="text-xs font-bold text-blue-400">
                {`${getDiscountPercentage(
                  itemPrices.strikeoffPrice,
                  itemPrices.price
                )}`}
              </span>
            )}
            <button
              onClick={() => setShowItemDetails(!showItemDetails)}
              disabled={itemDetails.closed || itemDetails.soldout}
            >
              {/* Description - using name as fallback */}
              <p className="text-xs text-left font-normal text-neutral-700 line-clamp-2 break-words pt-2 leading-snug">
                {itemDetails.description}
              </p>{" "}
            </button>
          </div>
        </div>
      </div>
      <div className="w-[36%] min-w-20 max-w-36 h-full flex flex-col items-center align-middle self-center">
        {/* Image and Add Button */}
        {isEmptyNullOrUndefinedString(itemDetails.itemUrl) ? (
          <div className="flex flex-col items-center align-middle self-center w-[80%]">
            {itemDetails.closed || itemDetails.soldout ? (
              <ItemStockOutButton />
            ) : (
              <AddItemButtonV2
                className="w-full h-fit bg-primary-50 rounded-[.25rem]"
                qty={qty}
                // onAdd={() => {
                //   if (hasCustomizations) {
                //     setShowItemDetails(!showItemDetails);
                //   } else {
                //     onAdd(itemDetails);
                //   }
                // }}
                // onRemove={() => onRemove(itemDetails)}
                onAdd={() => {
                  if (hasCustomizations) {
                    if (
                      cart &&
                      cart[itemDetails.sellerItemId]?.customizationList?.length
                    ) {
                      setShowMultiCustomization(!showMultiCustomization);
                    } else {
                      setShowItemDetails(!showItemDetails);
                    }
                  } else {
                    onAdd(itemDetails);
                  }
                }}
                onRemove={() => {
                  if (hasCustomizations) {
                    if (
                      cart &&
                      (cart[itemDetails.sellerItemId]?.customizationList ?? [])
                        .length > 1
                    ) {
                      setShowMultiCustomization(!showMultiCustomization);
                    } else {
                      console.log(
                        "decrease first item quantity in customization list"
                      );
                    }
                  } else {
                    onRemove(itemDetails);
                  }
                }}
                isDisabled={itemDetails.closed || itemDetails.soldout}
                unit={itemDetails?.unit || ""}
                btnConfig={{
                  showUnit: false,
                  btnType: "secondary",
                  iconSize: 18
                }}
              />
            )}
            {hasCustomizations &&
            !itemDetails.closed &&
            !itemDetails.soldout ? (
              <p className="text-xs font-normal text-typography-300 mt-1 tracking-wide">
                customisable
              </p>
            ) : (
              <p className="text-xs font-normal text-white mt-1 tracking-wide"></p>
            )}
          </div>
        ) : (
          <div
            className={cn("w-full flex flex-col items-center justify-center")}
          >
            <div className="w-full">
              <div className="relative align-middle">
                <Button
                  className={`border border-neutral-100 transition-all duration-300 ease-in-out rounded-xl aspect-square w-full disabled:bg-transparent disabled:opacity-100`}
                  onClick={() => setShowItemDetails(!showItemDetails)}
                  disabled={itemDetails.closed || itemDetails.soldout}
                >
                  <CustomImage
                    src={itemDetails.itemUrl}
                    alt={""}
                    className={cn(
                      "transition-all duration-300 ease-in-out rounded-xl object-cover aspect-square w-full",
                      itemDetails.closed || itemDetails.soldout
                        ? "grayscale cursor-not-allowed"
                        : ""
                    )}
                  />
                </Button>
                <div className="w-[80%] absolute -bottom-[0.5rem] left-1/2 -translate-x-1/2">
                  {itemDetails.closed || itemDetails.soldout ? (
                    <ItemStockOutButton />
                  ) : (
                    <AddItemButtonV2
                      className="w-full aspect-[3/1] bg-primary-50 rounded-[.25rem]"
                      qty={qty}
                      onAdd={() => {
                        console.log("Add Item");
                        if (hasCustomizations) {
                          setShowItemDetails(!showItemDetails);
                        } else {
                          onAdd(itemDetails);
                        }
                      }}
                      onRemove={() => onRemove(itemDetails)}
                      isDisabled={itemDetails.closed || itemDetails.soldout}
                      unit={itemDetails?.unit || ""}
                      btnConfig={{
                        showUnit: false,
                        btnType: "secondary",
                        iconSize: 18
                      }}
                    />
                  )}
                </div>
              </div>
              {hasCustomizations &&
              !itemDetails.closed &&
              !itemDetails.soldout ? (
                <div className="text-xs font-normal text-typography-200 mt-2 tracking-wide text-center">
                  customisable
                </div>
              ) : (
                <p className="text-xs font-normal text-white mt-2 tracking-wide"></p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ItemCard;
