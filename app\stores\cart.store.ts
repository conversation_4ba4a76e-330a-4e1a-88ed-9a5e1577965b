import { create } from "zustand";
import {
  Cart,
  AvailableItem,
  SellerOrderItem,
  AogList,
  CartItem,
  PrecheckOrderResponse,
  CustomizationItem
} from "~/types";
import { generateCustomizationKey } from "~/utils/cartUtils";
import { removeAllCarts } from "~/utils/localStorage";

interface CartStore {
  cart: Cart;
  cartKey: string | null;
  orderNote: string;
  showNoteFeature: boolean;
  cartOrderItemsMap: Map<number, SellerOrderItem>;
  cartClientType: "restaurant" | "retailer";
  totalAmount: number;
  itemCount: number;
  precheckResponse: PrecheckOrderResponse | null;
  setCart: (cart: Cart, cartKey: string) => void;
  addItem: (
    item: AvailableItem,
    cartKey: string,
    selectedAogs?: AogList[],
    variationId?: number
  ) => void;
  removeItem: (
    item: AvailableItem,
    cartKey: string,
    variationId?: number
  ) => void;
  addCustomizableItem: (
    item: AvailableItem,
    cartKey: string,
    selectedAogs?: AogList[],
    variationId?: number,
    customItemKey?: string
  ) => void;
  removeCustomizableItem: (
    item: AvailableItem,
    cartKey: string,
    variationId?: number,
    customItemKey?: string
  ) => void;
  increaseCustomizationQty: (
    item: AvailableItem,
    cartKey: string,
    customItemKey: string,
    incrementQty?: number
  ) => void;
  decreaseCustomizationQty: (
    item: AvailableItem,
    cartKey: string,
    customItemKey: string,
    decrementQty?: number
  ) => void;
  syncCart: (availableItems: AvailableItem[], cartKey: string) => void;
  clearCart: (cartKey: string | "all") => void;
  setOrderNote: (note: string, cartKey: string) => void;
  setShowNoteFeature: (show: boolean) => void;
  setCartOrderItemsMap: (
    cartOrderItemsMap: Map<number, SellerOrderItem>
  ) => void;
  setCartClientType: (cartClientType: "restaurant" | "retailer") => void;
  getCartItem: (sellerItemId: number) => CartItem | undefined;
  setPrecheckResponse: (precheckResponse: PrecheckOrderResponse) => void;
}

// Helper function to calculate cart totals
const calculateCartTotals = (cart: Cart) => {
  let totalAmount = 0;
  let itemCount = 0;

  Object.values(cart).forEach((item) => {
    totalAmount += item.amount;
    itemCount += 1; // TODO: ask darshan if this is correct
  });

  return { totalAmount, itemCount };
};

// Helper function to calculate cart totals
const calculateCartTotalsWithCustomization = (cart: Cart) => {
  let totalAmount = 0;
  let itemCount = 0;

  Object.values(cart).forEach((item) => {
    totalAmount += item.amount;
    itemCount += item.qty; // TODO: ask darshan if this is correct
  });

  return { totalAmount, itemCount };
};

const calculateCustomizationAmount = (
  customizationList: CustomizationItem[]
) => {
  return customizationList.reduce((acc, customization) => {
    return acc + customization.amount;
  }, 0);
};

const calculateCustomizationQty = (customizationList: CustomizationItem[]) => {
  return customizationList.reduce((acc, customization) => {
    return acc + customization.qty;
  }, 0);
};

const calculateCustomizationTotal = (
  customizationList: CustomizationItem[]
) => {
  return {
    amount: calculateCustomizationAmount(customizationList),
    qty: calculateCustomizationQty(customizationList)
  };
};

export const useCartStore = create<CartStore>((set, get) => ({
  cart: {},
  cartKey: null,
  orderNote: "",
  showNoteFeature: false,
  cartOrderItemsMap: new Map(),
  cartClientType: "retailer",
  totalAmount: 0,
  itemCount: 0,
  precheckResponse: null,
  setCart: (cart: Cart, cartKey: string) => {
    const { totalAmount, itemCount } = calculateCartTotals(cart);

    set({ cart, totalAmount, itemCount });
    // Sync with localStorage
    if (typeof window !== "undefined" && cartKey) {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(cart));
    }
  },

  getCartItem: (sellerItemId: number) => {
    const currentState = get();
    return currentState.cart[sellerItemId];
  },

  addItem: (item: AvailableItem, cartKey: string) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};

    // Ensure we have valid cart state
    if (!cart || typeof cart !== "object") {
      console.warn("Cart is not properly initialized");
      return;
    }

    const currentQty = cart[item.sellerItemId]?.qty || 0;
    const incrementOrderQty = item.incrementOrderQty || 1;

    if (currentQty + incrementOrderQty > item.maxAvailableQty) return;

    const newQty = currentQty + incrementOrderQty;

    // Calculate base amount for the main item
    const baseAmount = newQty * item.pricePerUnit;

    const newAmount = baseAmount;

    // Create new cart object to ensure immutability
    const updatedCart = {
      ...cart,
      [item.sellerItemId]: {
        itemId: item.sellerItemId,
        qty: newQty,
        amount: newAmount,
        cartKey
      }
    };

    // Calculate new total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    // Sync with localStorage
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
      } catch (error) {
        console.error("Error syncing cart with localStorage:", error);
      }
    }
  },

  removeItem: (item: AvailableItem, cartKey: string) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};
    const currentQty = cart[item.sellerItemId]?.qty || 0;
    const orderedQty = item.orderedQty || 0;
    const incrementOrderQty = item.incrementOrderQty || 1;

    if (currentQty <= orderedQty) return;

    const newQty = currentQty - incrementOrderQty;

    // Calculate base amount for the main item
    const baseAmount = newQty * item.pricePerUnit;

    // Total amount includes both base item and add-ons
    const newAmount = baseAmount;

    const updatedCart = { ...cart };
    if (newQty === 0) {
      delete updatedCart[item.sellerItemId];
    } else {
      updatedCart[item.sellerItemId] = {
        itemId: item.sellerItemId,
        qty: newQty,
        amount: newAmount,
        cartKey
      };
    }

    // Calculate new total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    if (typeof window !== "undefined") {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
    }
  },

  syncCart: (availableItems: AvailableItem[], cartKey: string) => {
    let initialCart: Cart = {};

    // First, sync ordered items
    availableItems?.forEach((item) => {
      if (item.orderedQty > 0) {
        // Calculate base amount for the main item
        const baseAmount = item.orderedQty * item.pricePerUnit;

        const totalAmount = baseAmount;

        initialCart[item.sellerItemId] = {
          itemId: item.sellerItemId,
          qty: item.orderedQty,
          amount: totalAmount,
          cartKey
        };
      }
    });

    // Then, sync with localStorage
    if (typeof window !== "undefined") {
      const storedCart = localStorage.getItem(`cart_${cartKey}`);
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          if (parsedCart) {
            initialCart = { ...initialCart, ...parsedCart };
          }
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    // Calculate total amount and item count
    const { totalAmount, itemCount } = calculateCartTotals(initialCart);

    // Update state with cart, cartKey, totalAmount, and itemCount
    set({ cart: initialCart, cartKey, totalAmount, itemCount });

    if (typeof window !== "undefined") {
      localStorage.setItem(`cart_${cartKey}`, JSON.stringify(initialCart));
    }
  },

  clearCart: (cartKey: string | "all") => {
    set({
      cart: {},
      cartKey: null,
      orderNote: "",
      totalAmount: 0,
      itemCount: 0
    });
    if (typeof window !== "undefined") {
      localStorage.removeItem(`cart_${cartKey}`);
      localStorage.removeItem(`orderNote_${cartKey}`);
    }
    if (cartKey === "all") {
      removeAllCarts();
    }
  },

  addCustomizableItem: (
    item: AvailableItem,
    cartKey: string,
    selectedAogs?: AogList[],
    variationId?: number,
    customItemKey?: string
  ) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};
    // Ensure we have valid cart state
    if (!cart || typeof cart !== "object") {
      console.warn("Cart is not properly initialized");
      return;
    }

    const incrementOrderQty = item.incrementOrderQty || 1;
    // Check if adding would exceed max available quantity
    if (
      (cart[item.sellerItemId]?.qty || 0) + incrementOrderQty >
      item.maxAvailableQty
    ) {
      return;
    }

    // Determine if this item has customizations
    const hasCustomizations =
      (item.aogList && item.aogList.length > 0) ||
      (item.itemVariationList && item.itemVariationList.length > 0);

    // Handle customizable and non-customizable items differently
    if (hasCustomizations) {
      // Generate customization key for the selected options
      const flatAddons =
        selectedAogs?.flatMap((aog) =>
          aog.addOnItemList.filter((addon) => addon.qty > 0)
        ) || [];

      const customKey = generateCustomizationKey(flatAddons, variationId);

      // Look for existing customization with the same key
      const existingCustomizationItem = cart[
        item.sellerItemId
      ]?.customizationList?.find((customItem) => customItem.key === customKey);

      // Get previous customization list or empty array
      const prevCustomizationList =
        cart[item.sellerItemId]?.customizationList || [];

      // Filter out current customization if it exists (we'll add updated version)
      const filteredCustomizationList = existingCustomizationItem
        ? prevCustomizationList
            .filter((customItem) => customItem.key !== customKey)
            .filter((customItem) => customItem.key !== customItemKey)
        : prevCustomizationList;

      // Calculate base price (taking into account variation if selected)
      let basePrice = item.pricePerUnit;
      if (variationId) {
        const variation = item.itemVariationList?.find(
          (v) => v.id === variationId
        );
        if (variation) {
          basePrice = variation.price;
        }
      }

      // Calculate addons price
      let addonsTotalPrice = 0;
      flatAddons.forEach((addon) => {
        addonsTotalPrice += addon.price * addon.qty;
      });

      // Calculate quantity and amount for this customization
      const customQty =
        (existingCustomizationItem?.qty || 0) + incrementOrderQty;
      const customAmount = (basePrice + addonsTotalPrice) * customQty;

      // Create updated customization item
      const updatedCustomItem = {
        key: customKey,
        qty: customQty,
        amount: customAmount,
        aogList: selectedAogs,
        flatAddons: flatAddons.length > 0 ? flatAddons : undefined,
        variationId: variationId
      };

      const finalCustomizationList = [
        ...filteredCustomizationList,
        updatedCustomItem
      ];

      // Calculate total quantity and amount for the item
      const { qty: totalItemQty, amount: totalItemAmount } =
        calculateCustomizationTotal(finalCustomizationList);

      // Create updated cart
      const updatedCart = {
        ...cart,
        [item.sellerItemId]: {
          itemId: item.sellerItemId,
          qty: totalItemQty,
          amount: totalItemAmount,
          cartKey,
          customizationList: finalCustomizationList
        }
      };

      // Calculate new cart totals
      const { totalAmount, itemCount } =
        calculateCartTotalsWithCustomization(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      // Update localStorage
      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }
    } else {
      // For non-customizable items, use the addItem logic
      const newQty = (cart[item.sellerItemId]?.qty || 0) + incrementOrderQty;
      const newAmount = newQty * item.pricePerUnit;

      const updatedCart = {
        ...cart,
        [item.sellerItemId]: {
          itemId: item.sellerItemId,
          qty: newQty,
          amount: newAmount,
          cartKey
        }
      };

      // Calculate new cart totals
      const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      // Update localStorage
      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }
    }
  },

  removeCustomizableItem: (
    item: AvailableItem,
    cartKey: string,
    variationId?: number,
    customItemKey?: string
  ) => {
    const currentState = get();

    // If cartKey changed, we need to load the correct cart first
    if (currentState.cartKey !== cartKey) {
      const storedCart =
        typeof window !== "undefined"
          ? localStorage.getItem(`cart_${cartKey}`)
          : null;
      if (storedCart) {
        try {
          const parsedCart = JSON.parse(storedCart) as Cart;
          currentState.cart = parsedCart;
        } catch (error) {
          console.error("Error parsing stored cart:", error);
        }
      }
    }

    const cart = currentState.cart || {};

    // Early exit if item doesn't exist in cart
    if (!cart[item.sellerItemId]) return;

    const incrementOrderQty = item.incrementOrderQty || 1;

    // Handle customizable items (with customizationList)
    if (cart[item.sellerItemId].customizationList && customItemKey) {
      const customizationList = cart[item.sellerItemId].customizationList || [];

      // Find the specific customization item to remove
      const customizationItemIndex = customizationList.findIndex(
        (custItem) => custItem.key === customItemKey
      );

      // If not found, return without changes
      if (customizationItemIndex === -1) return;

      const customizationItem = customizationList[customizationItemIndex];

      // Calculate new quantity and handle removal if qty reaches 0
      const newQty = customizationItem.qty - incrementOrderQty;

      // Update overall cart item qty
      const cartItemQty = cart[item.sellerItemId].qty - incrementOrderQty;

      const updatedCart = { ...cart };

      if (cartItemQty <= 0) {
        // Remove entire item if all customizations are removed
        delete updatedCart[item.sellerItemId];
      } else if (newQty <= 0) {
        // Remove just this customization if its qty is 0
        const updatedCustomizationList = [...customizationList];
        updatedCustomizationList.splice(customizationItemIndex, 1);

        // Calculate new total amount for this item
        let totalAmount = 0;
        updatedCustomizationList.forEach((customItem) => {
          totalAmount += customItem.amount;
        });

        updatedCart[item.sellerItemId] = {
          ...cart[item.sellerItemId],
          qty: cartItemQty,
          amount: totalAmount,
          customizationList: updatedCustomizationList
        };
      } else {
        // Just reduce the quantity of this customization
        const updatedCustomizationList = [...customizationList];

        // Recalculate amount for the specific customization
        const baseItemPrice = customizationItem.variationId
          ? item.itemVariationList?.find(
              (v) => v.id === customizationItem.variationId
            )?.price || item.pricePerUnit
          : item.pricePerUnit;

        // Calculate add-ons amount
        let addonsTotalPrice = 0;
        customizationItem.flatAddons?.forEach((addon) => {
          addonsTotalPrice += addon.price * addon.qty;
        });

        const newAmount = (baseItemPrice + addonsTotalPrice) * newQty;

        updatedCustomizationList[customizationItemIndex] = {
          ...customizationItem,
          qty: newQty,
          amount: newAmount
        };

        // Recalculate total amount for this item
        let totalAmount = 0;
        updatedCustomizationList.forEach((customItem) => {
          totalAmount += customItem.amount;
        });

        updatedCart[item.sellerItemId] = {
          ...cart[item.sellerItemId],
          qty: cartItemQty,
          amount: totalAmount,
          customizationList: updatedCustomizationList
        };
      }

      // Calculate new cart totals
      const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }

      return;
    }

    // Handle regular items (without customizationList)
    const currentQty = cart[item.sellerItemId].qty || 0;
    const orderedQty = item.orderedQty || 0;

    if (currentQty <= orderedQty) return;

    const newQty = currentQty - incrementOrderQty;

    if (newQty === 0) {
      // Remove the item completely
      const updatedCart = { ...cart };
      delete updatedCart[item.sellerItemId];

      // Calculate new cart totals
      const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }
    } else {
      // Just reduce the quantity
      const newAmount = newQty * item.pricePerUnit;

      const updatedCart = {
        ...cart,
        [item.sellerItemId]: {
          itemId: item.sellerItemId,
          qty: newQty,
          amount: newAmount,
          cartKey
        }
      };

      // Calculate new cart totals
      const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }
    }
  },

  increaseCustomizationQty: (
    item: AvailableItem,
    cartKey: string,
    customItemKey: string,
    incrementQty: number = 1
  ) => {
    const currentState = get();
    const cart = currentState.cart || {};

    // Early exit if item doesn't exist in cart
    if (!cart[item.sellerItemId]) return;

    const cartItem = cart[item.sellerItemId];
    if (!cartItem.customizationList) return;

    // Find the customization item
    const customizationIndex = cartItem.customizationList.findIndex(
      (custItem) => custItem.key === customItemKey
    );

    if (customizationIndex === -1) return;

    const customizationItem = cartItem.customizationList[customizationIndex];
    const newQty = customizationItem.qty + incrementQty;

    // Check if adding would exceed max available quantity
    if (cartItem.qty + incrementQty > item.maxAvailableQty) return;

    // Calculate new amount for this customization
    const baseItemPrice = customizationItem.variationId
      ? item.itemVariationList?.find(
          (v) => v.id === customizationItem.variationId
        )?.price || item.pricePerUnit
      : item.pricePerUnit;

    // Calculate add-ons amount
    let addonsTotalPrice = 0;
    customizationItem.flatAddons?.forEach((addon) => {
      addonsTotalPrice += addon.price * addon.qty;
    });

    const newAmount = (baseItemPrice + addonsTotalPrice) * newQty;

    // Update the customization item
    const updatedCustomizationList = [...cartItem.customizationList];
    updatedCustomizationList[customizationIndex] = {
      ...customizationItem,
      qty: newQty,
      amount: newAmount
    };

    // Calculate new total amount for the item
    let totalItemAmount = 0;
    updatedCustomizationList.forEach((customItem) => {
      totalItemAmount += customItem.amount;
    });

    // Update cart
    const updatedCart = {
      ...cart,
      [item.sellerItemId]: {
        ...cartItem,
        qty: cartItem.qty + incrementQty,
        amount: totalItemAmount,
        customizationList: updatedCustomizationList
      }
    };

    // Calculate new cart totals
    const { totalAmount, itemCount } =
      calculateCartTotalsWithCustomization(updatedCart);

    // Update state
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    // Update localStorage
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
      } catch (error) {
        console.error("Error syncing cart with localStorage:", error);
      }
    }
  },

  decreaseCustomizationQty: (
    item: AvailableItem,
    cartKey: string,
    customItemKey: string,
    decrementQty: number = 1
  ) => {
    const currentState = get();
    const cart = currentState.cart || {};

    // Early exit if item doesn't exist in cart
    if (!cart[item.sellerItemId]) return;

    const cartItem = cart[item.sellerItemId];
    if (!cartItem.customizationList) return;

    // Find the customization item
    const customizationIndex = cartItem.customizationList.findIndex(
      (custItem) => custItem.key === customItemKey
    );

    if (customizationIndex === -1) return;

    const customizationItem = cartItem.customizationList[customizationIndex];
    const newQty = customizationItem.qty - decrementQty;

    // If new quantity is 0 or less, remove the customization
    if (newQty <= 0) {
      const updatedCustomizationList = cartItem.customizationList.filter(
        (custItem) => custItem.key !== customItemKey
      );

      // If no customizations left, remove the entire item
      if (updatedCustomizationList.length === 0) {
        const updatedCart = { ...cart };
        delete updatedCart[item.sellerItemId];

        // Calculate new cart totals
        const { totalAmount, itemCount } = calculateCartTotals(updatedCart);

        // Update state
        set({ cart: updatedCart, cartKey, totalAmount, itemCount });

        // Update localStorage
        if (typeof window !== "undefined") {
          try {
            localStorage.setItem(
              `cart_${cartKey}`,
              JSON.stringify(updatedCart)
            );
          } catch (error) {
            console.error("Error syncing cart with localStorage:", error);
          }
        }
        return;
      }

      // Calculate new total amount for the item
      let totalItemAmount = 0;
      updatedCustomizationList.forEach((customItem) => {
        totalItemAmount += customItem.amount;
      });

      // Update cart
      const updatedCart = {
        ...cart,
        [item.sellerItemId]: {
          ...cartItem,
          qty: cartItem.qty - decrementQty,
          amount: totalItemAmount,
          customizationList: updatedCustomizationList
        }
      };

      // Calculate new cart totals
      const { totalAmount, itemCount } =
        calculateCartTotalsWithCustomization(updatedCart);

      // Update state
      set({ cart: updatedCart, cartKey, totalAmount, itemCount });

      // Update localStorage
      if (typeof window !== "undefined") {
        try {
          localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
        } catch (error) {
          console.error("Error syncing cart with localStorage:", error);
        }
      }
      return;
    }

    // Calculate new amount for this customization
    const baseItemPrice = customizationItem.variationId
      ? item.itemVariationList?.find(
          (v) => v.id === customizationItem.variationId
        )?.price || item.pricePerUnit
      : item.pricePerUnit;

    // Calculate add-ons amount
    let addonsTotalPrice = 0;
    customizationItem.flatAddons?.forEach((addon) => {
      addonsTotalPrice += addon.price * addon.qty;
    });

    const newAmount = (baseItemPrice + addonsTotalPrice) * newQty;

    // Update the customization item
    const updatedCustomizationList = [...cartItem.customizationList];
    updatedCustomizationList[customizationIndex] = {
      ...customizationItem,
      qty: newQty,
      amount: newAmount
    };

    // Calculate new total amount for the item
    let totalItemAmount = 0;
    updatedCustomizationList.forEach((customItem) => {
      totalItemAmount += customItem.amount;
    });

    // Update cart
    const updatedCart = {
      ...cart,
      [item.sellerItemId]: {
        ...cartItem,
        qty: cartItem.qty - decrementQty,
        amount: totalItemAmount,
        customizationList: updatedCustomizationList
      }
    };

    // Calculate new cart totals
    const { totalAmount, itemCount } =
      calculateCartTotalsWithCustomization(updatedCart);

    // Update state
    set({ cart: updatedCart, cartKey, totalAmount, itemCount });

    // Update localStorage
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(`cart_${cartKey}`, JSON.stringify(updatedCart));
      } catch (error) {
        console.error("Error syncing cart with localStorage:", error);
      }
    }
  },

  setOrderNote: (note: string, cartKey: string) => {
    set({ orderNote: note });
    // Sync with localStorage
    if (typeof window !== "undefined" && cartKey) {
      if (note) {
        localStorage.setItem(`orderNote_${cartKey}`, note);
      } else {
        localStorage.removeItem(`orderNote_${cartKey}`);
      }
    }
  },

  setShowNoteFeature: (show: boolean) => {
    set({ showNoteFeature: show });
  },

  setCartOrderItemsMap: (cartOrderItemsMap: Map<number, SellerOrderItem>) => {
    set({ cartOrderItemsMap });
  },

  setCartClientType: (cartClientType: "restaurant" | "retailer") => {
    set({ cartClientType });
  },

  setPrecheckResponse: (precheckResponse: PrecheckOrderResponse) => {
    set({ precheckResponse });
  }
}));
