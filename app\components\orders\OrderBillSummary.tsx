import { FC } from "react";
import { Order } from "~/types";
import BillRow from "./BillRow";
import PaymentDetails from "./PaymentDetails";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { roundOff } from "~/utils/roundOff";

interface OrderBillSummaryProps {
  order: Order;
}

const OrderBillSummary: FC<OrderBillSummaryProps> = ({ order }) => {
  const codAmount = order?.isPending ? 0 : order?.codAmount || 0;
  const onlineAmount =
    order?.totalAmount - order?.codAmount - order?.delayPaymentPendingAmount;

  return (
    <section className="bg-white rounded-lg shadow">
      <div className="p-4 flex flex-col gap-4">
        <h3 className="text-sm text-gray-500">Bill Summary</h3>

        <div className="border border-dashed rounded-lg px-3 py-1">
          <BillRow
            label="Item Total"
            value={order.totalOrderAmount}
            className="border-b"
          />

          {order.discountAmount > 0 && (
            <BillRow
              label="Discount Amount"
              value={-1 * order.discountAmount}
              className="border-b"
            />
          )}

          <BillRow label="Delivery Charges" value={order.deliveryCharges} />

          {/* {order.packagingCharges > 0 && (
            <BillRow
              label="Packaging Charges"
              value={order.packagingCharges}
              className="border-t"
            />
          )} */}

          {/* {order.platformFee > 0 && (
            <BillRow
              label="Platform Fee"
              value={order.platformFee}
              className="border-t"
            />
          )} */}

          {order.totalTaxAmount > 0 && (
            <Popover>
              <PopoverTrigger className="w-full">
                <BillRow
                  label="GST & Other Charges"
                  value={order.totalTaxAmount}
                  className="border-t"
                />
              </PopoverTrigger>
              <PopoverContent side="top" className="p-3 rounded-lg">
                <div className="mb-1">
                  <span className="font-bold text-typography-800">
                    GST & Other
                  </span>
                </div>
                <div className="flex flex-col gap-2">
                  {order.packagingCharges !== undefined && order.packagingCharges > 0 && (
                    <div className="flex flex-row gap-3 justify-between">
                      <div>
                        <p className="text-sm font-semibold text-typography-600">
                          Packaging charges
                        </p>
                        <p className="text-xs text-typography-400">
                          Helps cover spill-proof packaging costs.
                        </p>
                      </div>
                      <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                        ₹ {roundOff(order.packagingCharges, true)}
                      </span>
                    </div>
                  )}
                  {order.packagingTax !== undefined && order.packagingTax > 0 && (
                    <div className="flex flex-row gap-3 justify-between">
                      <div>
                        <p className="text-sm font-semibold text-typography-600">
                          GST on packaging charges
                        </p>
                        <p className="text-xs text-typography-400">
                          5% Government-mandated tax.
                        </p>
                      </div>
                      <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                        ₹ {roundOff(order.packagingTax, true)}
                      </span>
                    </div>
                  )}
                  {order.itemsTax !== undefined  && order.itemsTax > 0 && (
                    <div className="flex flex-row gap-3 justify-between">
                      <div>
                        <p className="text-sm font-semibold text-typography-600">
                          GST on item total
                        </p>
                        <p className="text-xs text-typography-400">
                          5% Government-mandated tax.
                        </p>
                      </div>
                      <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                        ₹ {roundOff(order.itemsTax, true)}
                      </span>
                    </div>
                  )}
                  {order.deliveryTax !== undefined && order.deliveryTax > 0 && (
                    <div className="flex flex-row gap-3 justify-between">
                      <div>
                        <p className="text-sm font-semibold text-typography-600">
                          GST on delivery charges
                        </p>
                        <p className="text-xs text-typography-400">
                          GST on delivery charges
                        </p>
                      </div>
                      <span className="text-sm font-semibold text-typography-600 whitespace-nowrap">
                        ₹ {roundOff(order.deliveryTax, true)}
                      </span>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          )}

          <BillRow
            label="Total Amount"
            value={order.totalAmount}
            valueColor="text-blue-500"
            isTotal
            className="border-t border-gray-300"
          />

          {/* <PaymentDetails
            codAmount={codAmount}
            onlineAmount={onlineAmount}
            delayPaymentPendingAmount={order.delayPaymentPendingAmount}
          /> */}
        </div>

        {order.walletAmount !== undefined &&
          order.cashPaid !== undefined &&
          order.balanceTobePaid !== undefined && (
            <div className="border border-dashed rounded-lg px-3 py-1">
              <BillRow label="Cash Paid" value={order.cashPaid} />
              <BillRow
                label="Online Payment Paid "
                value={order.walletAmount}
                className="border-t"
              />
              <BillRow
                label="Balance to be paid "
                value={order.balanceTobePaid}
                valueColor="text-blue-500"
                isTotal
                className="border-t border-gray-300"
              />
            </div>
          )}
      </div>
    </section>
  );
};

export default OrderBillSummary;
