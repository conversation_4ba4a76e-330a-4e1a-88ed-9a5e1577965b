import { ActionFunction, LoaderFunction, redirect } from "@remix-run/node";
import {
  json,
  useLoaderData,
  useLocation,
  useNavigate,
  useSearchParams
} from "@remix-run/react";
import { useState } from "react";
import { BackNavHeader } from "~/components/BackNavHeader";
import Button from "~/components/Button";
import {
  createSupportTicket,
  getAllSupportTickets
} from "~/services/buyer.service";
import { User } from "~/types";
import { DecodedToken, SupportTicket } from "~/types/user";
import {
  commitSession,
  destroySession,
  getSession
} from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import CreateSupportTicket from "~/components/help/CreateSupportTicket";
import { ChevronRight } from "lucide-react";
import SupportTicketCard from "~/components/help/SupportTicketCard";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { useRequireAuth } from "~/hooks/useRequireAuth";
interface LoaderData {
  tickets: SupportTicket[];
  user: User;
  mobileNumber?: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;
  const url = new URL(request.url);
  const appSource = url.searchParams.get("source");

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, tickets: [] });
  }

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appSource, appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${url.pathname}`, { headers });
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;
    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      session = await getSession();
      session.set("appConfig", { appSource, appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/login?redirectTo=${url.pathname}`, { headers });
    }

    const response = await getAllSupportTickets(user.userId, request);

    return createClientResponse(
      request,
      {
        tickets: response.data || [],
        user,
        mobileNumber: decoded.userDetails.mobileNumber
      },
      response
    );
  } catch (error: unknown) {
    console.error("Error decoding access_token:", error);
    return redirect("/home");
  }
};

interface ActionData {
  success?: boolean;
  error?: string;
}
export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: false, error: "Authentication required" });
  }

  if (!access_token || !user) {
    return redirect("/login");
  }

  const formData = await request.formData();
  const description = formData.get("description") as string;
  const issueType = (formData.get("issueType") as string) || "others";
  const orderGroupIdStr = formData.get("orderGroupId") as string | null;
  const orderGroupId = orderGroupIdStr
    ? parseInt(orderGroupIdStr, 10)
    : undefined;

  // Set ticket type based on whether it's order-related
  const ticketType = orderGroupId
    ? "order_related"
    : issueType.toLowerCase().replace(/\s+/g, "_");

  const response = await createSupportTicket(
    {
      userId: user.userId,
      // appSellerId: 6,
      ticketType,
      status: "OPEN",
      requestedCallBack: true,
      description: description || "",
      orderGroupId
    },
    request
  );

  return createClientResponse<ActionData, SupportTicket>(
    request,
    { success: true },
    response
  );
};

export default function Help() {
  const loader = useLoaderData<LoaderData>();
  const { tickets } = loader;
  const [searchParams] = useSearchParams();
  const action = searchParams.get("action");
  const orderId = searchParams.get("orderId") || "";
  const location = useLocation();
  const appDomain = useAppConfigStore((state) => state.appDomain);
  const orderIdFromState = location.state?.orderId;
  const effectiveOrderId = orderId || orderIdFromState || "";

  const [createTicket, setCreateTicket] = useState(
    action === "create" || appDomain === "RET11"
  );
  console.log("effectiveOrderId", effectiveOrderId);

  return (
    <div className="h-screen flex flex-col">
      {/* create new ticket */}

      {createTicket ? (
        <CreateSupportTicket
          setCreateTicket={setCreateTicket}
          orderId={effectiveOrderId}
        />
      ) : (
        <HelpAndSupport setCreateTicket={setCreateTicket} tickets={tickets} />
      )}
    </div>
  );
}
function HelpAndSupport({
  setCreateTicket,
  tickets
}: {
  setCreateTicket: (value: boolean) => void;
  tickets: SupportTicket[];
}) {
  const navigate = useNavigate();
  const { appSource, appDomain } = useAppConfigStore((state) => state);
  useRequireAuth();

  return (
    <>
      <BackNavHeader
        backButton={
          appSource === "whatsappchat" && appDomain !== "RET11" ? false : true
        }
        buttonText="Help & Support"
        handleBack={() => navigate("/home/<USER>")}
      />
      <div className="flex flex-col my-2 p-4 bg-white">
        <Button
          onClick={() => setCreateTicket(true)}
          className="flex flex-row justify-between items-center"
        >
          <div className="flex flex-col items-start">
            <div className="text-teal-600 text-sm">Create New Ticket</div>
            <span className="text-xs text-gray-500 font-light">
              Having an issue? Raise a request to our support team
            </span>
          </div>
          <div>
            <ChevronRight className="text-gray-400" />
          </div>
        </Button>
      </div>
      <div className="flex-grow flex flex-col items-start mt-2 h-full">
        <div className="font-normal text-sm px-4 py-1 w-full">
          All My Requests
        </div>
        <div className="flex-grow flex flex-col px-4 overflow-y-scroll w-full max-h-[600px]">
          {tickets.map((ticket, i) => (
            <SupportTicketCard key={i} ticketDetails={ticket} />
          ))}
        </div>
      </div>
    </>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(-1)} />;
}
