import { X } from "lucide-react";
import React from "react";
import { SellerOrderItem } from "~/types";
import ItemRow from "@components/ItemRow";
import { useCartStore } from "~/stores/cart.store";
import RestaurantItemRow from "../cart/RestaurantItemRow";
interface SoldOutItemsProps {
  items: SellerOrderItem[];
  onClose: () => void;
}

export const SoldOutItems: React.FC<SoldOutItemsProps> = ({
  items,
  onClose
}) => {
  const { cartClientType } = useCartStore();
  const soldOutItems = items.filter(
    (item) => item.isSoldOut && item.quantity === 0
  );

  if (soldOutItems.length === 0) return null;

  return (
    <div className="mx-3 transition-all duration-300 ease-in-out p-3 border border-secondary-100 bg-[#FEF5F6] rounded-2xl flex flex-col gap-3">
      <div className="flex justify-between align-middle w-100% text-md text-secondary-800 font-bold tracking-wide">
        <span>
          {soldOutItems.length}{" "}
          {`${soldOutItems.length > 1 ? "items are" : "item is"} not in stock`}
        </span>
        <button
          className="bg-white h-fit w-fit rounded-full self-center"
          onClick={onClose}
        >
          <X
            color="white"
            size={"0.875rem"}
            className="bg-neutral-600 rounded-full p-[0.125rem]"
          />
        </button>
      </div>
      <div className="flex flex-col gap-1">
        {soldOutItems.map((item) => (
          <React.Fragment key={item.sellerItemId}>
            {cartClientType === "retailer" ? (
              <ItemRow itemDetails={item} />
            ) : (
              <RestaurantItemRow itemDetails={item} />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};
