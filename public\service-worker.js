// This is a basic service worker that doesn't do much without HTTPS
self.addEventListener('install', (event) => {
    console.log('Service Worker installed');
});

self.addEventListener('activate', (event) => {
    console.log('Service Worker activated');
});

self.addEventListener('fetch', (event) => {
    // Without HTTPS, we can't do much here
    // but this listener is required for the service worker to be valid
});
