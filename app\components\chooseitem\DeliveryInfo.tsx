import { <PERSON><PERSON> } from "@headlessui/react";
// import dayjs from "dayjs";
import React, { useState } from "react";
import TruncatedText from "../TruncatedText";
import { CiEdit } from "react-icons/ci";
import { useNavigate } from "@remix-run/react";
import { AddressDto } from "~/types/address.types";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { MapPin } from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface NotDeliveringMessageProps {
  onAddressClick: () => void;
  visible?: boolean;
}

const NotDeliveringMessage: React.FC<NotDeliveringMessageProps> = ({
  onAddressClick,
  visible = true
}) => {
  if (!visible) return null;

  return (
    <Button className="w-full" onClick={onAddressClick}>
      <div className="flex flex-col w-full items-start gap-2 rounded-md bg-orange-100 p-2 text-black mt-2">
        <div className="text-xs font-bold p-2 bg-orange-800 rounded-md text-white">
          NOT DELIVERING
        </div>
        <p className="text-xs font-light">
          {"We do not deliver in your area since it's far away."}
        </p>
      </div>
    </Button>
  );
};

interface SellerProps {
  deliveryDate: string;
  deliveryTime: string;
  address?: string;
  sellerLogo?: string;
  sellerName?: string;
  estDeliveryTime: string;
  defaultAddress: AddressDto;
  onAddressClick?: () => void;
}

const DeliveryInfo: React.FC<SellerProps> = ({
  // deliveryDate,
  // deliveryTime,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  address,
  sellerLogo,
  sellerName,
  estDeliveryTime,
  defaultAddress,
  onAddressClick
}) => {
  const [showSellerInfo, setShowSellerInfo] = useState(false);
  const navigate = useNavigate();
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { appDomain } = useAppConfigStore((state) => state);

  const handleAddressClick = () => {
    if (onAddressClick) {
      onAddressClick();
    } else {
      if (appDomain === "RET11") {
        navigate(`/changeaddress?redirectTo=/home/<USER>/rsrp`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      } else {
        navigate(`/changeaddress?redirectTo=/chooseitems`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      }
    }
  };
  return (
    <>
      {
        <div className="relative flex flex-col justify-between items-center px-3 py-2">
          <div className="flex justify-between items-center w-full">
            <div className="">
              <div className="flex flex-col items-start">
                {itemOptionsData?.defaultAddress.buyerInServiceArea && (
                  <>
                    <p className="text-xs font-light">
                      Delivery{" "}
                      {estDeliveryTime.includes("min") ||
                      estDeliveryTime.includes("hours")
                        ? "in"
                        : "at"}{" "}
                    </p>
                    <div className="font-bold text-2xl">
                      <span>{estDeliveryTime}</span>
                    </div>
                  </>
                )}
                {defaultAddress &&
                  parseInt(defaultAddress.latitude) !== 0 &&
                  parseInt(defaultAddress.longitude) !== 0 && (
                    <Button
                      className="text-sm relative flex items-center gap-2 mr-1 w-full max-w-[18rem]"
                      onClick={handleAddressClick}
                    >
                      <span>
                        <MapPin className="w-4 h-4" />
                      </span>
                      <span className="text-xs font-bold">
                        {defaultAddress.name?.toLocaleUpperCase()}
                      </span>
                      <span>{" - "}</span>
                      <TruncatedText text={defaultAddress.address} />
                      {/* <div className="w-5 h-5 text-gray-100"> */}
                      <CiEdit className="absolute left-[101%] bottom-1 w-5 h-5" />
                      {/* </div> */}
                    </Button>
                  )}
              </div>
            </div>
            {defaultAddress &&
              parseInt(defaultAddress.latitude) !== 0 &&
              parseInt(defaultAddress.longitude) !== 0 && (
                <div>
                  <Button>
                    <div
                      className={`flex items-center justify-center w-12 h-12 rounded-full  relative overflow-hidden border border-white bg-white`}
                    >
                      {sellerLogo && (
                        <img src={sellerLogo} alt="" className={`absolute `} />
                      )}
                    </div>
                  </Button>
                </div>
              )}
          </div>
          {!itemOptionsData?.defaultAddress.buyerInServiceArea &&
            parseInt(defaultAddress.latitude) !== 0 &&
            parseInt(defaultAddress.longitude) !== 0 && (
              <NotDeliveringMessage
                onAddressClick={handleAddressClick}
                visible={!itemOptionsData?.defaultAddress.buyerInServiceArea}
              />
            )}
          {showSellerInfo && sellerName && (
            <SellerInfoModal
              sellerName={sellerName}
              setShowSellerInfo={setShowSellerInfo}
            />
          )}
        </div>
      }
    </>
  );
};

const SellerInfoModal: React.FC<{
  sellerName: string;
  setShowSellerInfo: (value: boolean) => void;
}> = ({ sellerName, setShowSellerInfo }) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="flex flex-col items-center w-10/12 bg-white rounded-xl p-4">
        <span className="font-medium text-lg mb-4">Seller Details</span>
        <span className="mb-4">{sellerName}</span>
        <Button
          className=" bg-primary rounded-md text-white p-2  w-16"
          onClick={() => setShowSellerInfo(false)}
        >
          Close
        </Button>
      </div>
    </div>
  );
};

export default DeliveryInfo;
