import { FC, useRef, useState } from "react";
import { Order, OrderStatus, OrderItem } from "~/types";
import dayjs from "dayjs";
import TimelineHeader from "./TimelineHeader";
import TimelineDeliveryStatus from "./TimelineDeliveryStatus";
import TimelineSteps, { TimelineStep } from "./TimelineSteps";
import TimelineDeliveryPartner from "./TimelineDeliveryPartner";
import TimelineDeliveryTime from "./TimelineDeliveryTime";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { Download } from "lucide-react";
import InvoicePdf from "~/components/invoice/InvoicePdf";

interface OrderTimelineProps {
  orderDetails: Order;
  orderItems?: Partial<OrderItem>[];
}

const OrderTimeline: FC<OrderTimelineProps> = ({
  orderDetails,
  orderItems = []
}) => {
  const status = orderDetails.status as OrderStatus;
  console.log("status", status);
  const isDelivered = status === "Delivered";
  const isCancelled = status === "Cancelled";

  // Add state to control rendering the invoice for PDF generation
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  // Add loading state for better UX
  const [isLoading, setIsLoading] = useState(false);

  // Use useRef instead of the hook
  const pdfRef = useRef<HTMLDivElement>(null);

  const getTimeDisplay = () => {
    const time = !orderDetails.deliveryTime
      ? `${orderDetails.deliveryDate} ${orderDetails.estDeliveryTime}`
      : `${orderDetails.deliveryDate} ${orderDetails.deliveryTime}`;

    if (isDelivered) {
      return {
        label: "Delivered on",
        time: dayjs(time).format("DD MMM, h:mm A")
      };
    }

    if (isCancelled) {
      return {
        label: "Cancelled on",
        time: dayjs(time).format("DD MMM, h:mm A")
      };
    }
    return {
      label: "Arriving by",
      time: "TOMORROW, 7 AM - 9 AM"
    };
  };

  const getTimelineSteps = (): TimelineStep[] => {
    const allSteps: { [key: string]: TimelineStep } = {
      orderPlaced: {
        label: "Order Placed",
        status: "pending"
      },
      orderConfirmed: {
        label: "Order Confirmed",
        status: "pending"
      },
      orderPacked: {
        label: "Order Packed",
        status: "pending"
      },
      partnerAssigned: {
        label: "Partner Assigned",
        status: "pending"
      },
      pickedUp: {
        label: "Order Picked-up",
        status: "pending"
      },
      outForDelivery: {
        label: "Out for Delivery",
        status: "pending"
      },
      arrivedAtLocation: {
        label: "Arrived at location",
        status: "pending"
      }
    };

    // Update step statuses based on current order status
    switch (status) {
      case "Created":
        allSteps.orderPlaced.status = "completed";
        break;
      case "Accepted":
        allSteps.orderPlaced.status = "completed";
        allSteps.orderConfirmed.status = "completed";
        break;
      case "Assigned":
        allSteps.orderPlaced.status = "completed";
        allSteps.orderConfirmed.status = "completed";
        allSteps.partnerAssigned.status = "completed";
        break;
      case "Packed":
        allSteps.orderPlaced.status = "completed";
        allSteps.orderConfirmed.status = "completed";
        allSteps.partnerAssigned.status = "completed";
        allSteps.orderPacked.status = "completed";
        break;
      case "PickedUp":
        allSteps.orderPlaced.status = "completed";
        allSteps.orderConfirmed.status = "completed";
        allSteps.partnerAssigned.status = "completed";
        allSteps.orderPacked.status = "completed";
        allSteps.pickedUp.status = "completed";
        allSteps.outForDelivery.status = "completed";
        break;
      case "Dispatched":
        allSteps.orderPlaced.status = "completed";
        allSteps.orderConfirmed.status = "completed";
        allSteps.partnerAssigned.status = "completed";
        allSteps.orderPacked.status = "completed";
        allSteps.pickedUp.status = "completed";
        allSteps.outForDelivery.status = "completed";
        break;
      default:
        break;
    }

    // Convert steps object to array and filter completed steps to show only last 4
    const stepsArray = Object.values(allSteps);
    const completedSteps = stepsArray.filter(
      (step) => step.status === "completed"
    );
    const activeAndPendingSteps = stepsArray.filter(
      (step) => step.status !== "completed"
    );

    // Show only the last 3 completed steps + current/next step
    const relevantCompletedSteps = completedSteps.slice(-3);
    const nextSteps = activeAndPendingSteps.slice(
      0,
      4 - relevantCompletedSteps.length
    );

    return [...relevantCompletedSteps, ...nextSteps];
  };

  const timelineSteps = getTimelineSteps();
  const timeDisplay = getTimeDisplay();
  const { networkConfig } = useAppConfigStore();

  const handleDownloadInvoice = async () => {
    try {
      setIsLoading(true);
      // Set the state to render the invoice
      setIsGeneratingPdf(true);

      // Wait a moment for the component to render
      setTimeout(async () => {
        try {
          // Dynamically import the generatePDF function only when needed
          const { default: generatePDF } = await import("react-to-pdf");

          // Use generatePDF directly with the ref
          await generatePDF(pdfRef, {
            filename: `invoice-${orderDetails.id}.pdf`,
            page: {
              margin: 10,
              format: "a4",
              orientation: "portrait"
            }
          });
        } catch (error) {
          console.error("Error generating PDF:", error);
        } finally {
          // Hide the invoice again
          setIsGeneratingPdf(false);
          setIsLoading(false);
        }
      }, 100);
    } catch (error) {
      setIsLoading(false);
      setIsGeneratingPdf(false);
      console.error("Error loading PDF generator:", error);
    }
  };

  if (isDelivered || isCancelled) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 flex flex-col gap-2">
        <TimelineHeader
          sellerLogo={networkConfig?.businessLogo}
          sellerName={orderDetails.sellerName}
          sellerAddress={orderDetails.farmers[0]?.sellerAddress}
        />
        <div className="border-b border-neutral-200"></div>
        <TimelineDeliveryStatus
          label={timeDisplay.label}
          time={timeDisplay.time}
          status={status}
        />
        {status === "Delivered" && (
          <div className="flex justify-end -mb-1">
            <button
              className="text-teal-600 text-xs flex items-center space-x-1 border border-neutral-400 rounded-[.25rem] p-1"
              onClick={handleDownloadInvoice}
              disabled={isLoading}
            >
              <Download size={12} />
              <span>{isLoading ? "Generating..." : "Download Invoice"}</span>
            </button>
          </div>
        )}

        {/* Render the invoice component in a way that's conditionally visible but out of the normal flow */}
        {isGeneratingPdf && (
          <div
            style={{
              position: "fixed",
              left: "-9999px",
              top: 0,
              width: "800px",
              height: "auto",
              backgroundColor: "white",
              zIndex: -1000
            }}
          >
            <InvoicePdf
              ref={pdfRef}
              orderDetails={orderDetails}
              items={orderItems as Partial<OrderItem>[]}
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <TimelineHeader
        sellerLogo={networkConfig?.businessLogo}
        sellerName={orderDetails.sellerName}
        sellerAddress={orderDetails.farmers[0]?.sellerAddress}
      />
      <div className="border-t border-gray-200 my-4"></div>
      <div className="border border-dashed border-gray-200 rounded-lg p-2">
        <TimelineDeliveryTime
          label={timeDisplay.label}
          time={timeDisplay.time}
        />
        <TimelineSteps steps={timelineSteps} />
      </div>
      {orderDetails.delPartnerName &&
        status !== "Created" &&
        status !== "Accepted" && (
          <TimelineDeliveryPartner
            name={orderDetails.delPartnerName}
            phone={orderDetails.delPartnerNumber}
          />
        )}
    </div>
  );
};

export default OrderTimeline;
