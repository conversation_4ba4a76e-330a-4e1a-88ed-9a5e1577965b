import { FC } from "react";
import { Order } from "~/types";
import OrderItem from "./OrderItem";
import RestaurantOrderItem from "./RestaurantOrderItem";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { FreeOrderItem } from "./FreeOrderItem";

interface OrderItemListProps {
  order: Order;
}

const OrderItemList: FC<OrderItemListProps> = ({ order }) => {

  const { appDomain } = useAppConfigStore((state) => state);
  const allItems = order?.farmers?.flatMap((farmer) => farmer?.items);
  // Separate free and non-free items when appDomain is not RET11
  const freeItems = appDomain !== "RET11" ? allItems?.filter((item) => item?.freeItem) : [];
  const nonFreeItems = appDomain !== "RET11" ? allItems?.filter((item) => !item?.freeItem) : allItems;

  return (
    <section className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-2">
        <h3 className="text-sm text-gray-500">Your Order</h3>
        <span className="text-sm text-gray-500">
          {order.totalItemCount} items
        </span>
      </div>
      <div className="divide-y divide-gray-100">
        {appDomain === "RET11" ? (
          // Render RestaurantOrderItem for RET11
          allItems.map((item, index) => (
            <RestaurantOrderItem
              key={`${item.orderId}-${index}`}
              item={item}
            />
          ))
        ) : (
          // Render free items first, then non-free items
          <>
            {freeItems?.map((item, index) => (
              <FreeOrderItem
                key={`${item.orderId}-${index}`}
                item={item}
              />
            ))}
            {nonFreeItems?.map((item, index) => (
              <OrderItem
                key={`${item.orderId}-${index}`}
                item={item}
              />
            ))}
          </>
        )}
      </div>
    </section>
  );
};

export default OrderItemList;
