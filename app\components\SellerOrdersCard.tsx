import React, { useState } from "react";
import {
  PrecheckOrderResponse,
  SellerOrderItem,
  FulfillmentType
} from "~/types";
import { useNavigate } from "@remix-run/react";
import { useCartStore } from "~/stores/cart.store";
import { SoldOutItems } from "./orders/SoldOutItems";
import { OrderItemsList } from "./orders/OrderItemsList";
import { AddMoreItems } from "./orders/AddMoreItems";
import { PaymentDetails } from "./orders/PaymentDetailsCart";
import Toast from "./Toast";
import NotePopup from "./NotePopup";
import { CouponDTO } from "~/types/coupon.types";
import { ApplyCouponCard, AppliedCouponCard } from "./cart/CouponCards";
import { DeliveryOptions } from "./cart/DeliveryOptions";

interface SellerOrdersCardProps {
  approxPricing: boolean;
  orderDetails: PrecheckOrderResponse;
  handleBack: () => void;
  onItemUpdate?: () => void;
  appliedCoupon?: CouponDTO | null;
  onRemoveCoupon?: () => void;
  showCoupon?: boolean;
  onApplyCoupon?: () => void;
  selectedFulfillmentType: FulfillmentType;
  onFulfillmentChange: (option: FulfillmentType) => void;
}

const SellerOrdersCard: React.FC<SellerOrdersCardProps> = ({
  approxPricing,
  orderDetails,
  onItemUpdate,
  appliedCoupon,
  onRemoveCoupon,
  showCoupon,
  onApplyCoupon,
  selectedFulfillmentType,
  onFulfillmentChange
}) => {
  const navigate = useNavigate();
  const [showSoldOutItems, setShowSoldOutItems] = useState(true);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState<
    "success" | "error" | "warning" | "info" | "itemLimited"
  >("itemLimited");
  const [showNotePopup, setShowNotePopup] = useState(false);

  const {
    cart,
    addItem: addToCart,
    removeItem: removeFromCart,
    orderNote,
    showNoteFeature,
    setOrderNote
  } = useCartStore((state) => state);

  const handleAddItem = (item: SellerOrderItem) => {
    const currentQty = cart[item.sellerItemId]?.qty || 0;
    const incrementOrderQty = item.availableCartItem.incrementOrderQty || 1;
    if (!orderDetails.cartKey) return;

    if (
      currentQty + incrementOrderQty >
      item.availableCartItem.maxAvailableQty
    ) {
      setToastMessage(
        `Sorry, we have limited quantity available for this item!`
      );
      setToastType("itemLimited");
      setShowToast(true);
      return;
    }

    addToCart(item.availableCartItem, orderDetails.cartKey);
    onItemUpdate?.();
  };

  const handleRemoveItem = (item: SellerOrderItem) => {
    const currentQty = cart[item.sellerItemId]?.qty || 0;
    const orderedQty = item.availableCartItem.orderedQty || 0;

    if (!orderDetails.cartKey) return;

    if (currentQty <= orderedQty) {
      setToastMessage(
        `Cannot reduce below ordered quantity of ${orderedQty} units`
      );
      setToastType("warning");
      setShowToast(true);
      return;
    }

    removeFromCart(item.availableCartItem, orderDetails.cartKey);
    onItemUpdate?.();
  };

  const handleOpenNotePopup = () => {
    setShowNotePopup(true);
  };

  const handleCloseNotePopup = () => {
    setShowNotePopup(false);
  };

  const handleSaveNote = (note: string) => {
    if (orderDetails.cartKey) {
      setOrderNote(note, orderDetails.cartKey);
    }
  };

  const handleClearNote = () => {
    if (orderDetails.cartKey) {
      setOrderNote("", orderDetails.cartKey);
    }
  };

  return (
    <div className="pt-4 flex flex-col gap-4 overflow-y-auto h-[80vh] no-scrollbar transition-all duration-300 ease-in-out pb-40">
      {showSoldOutItems && (
        <SoldOutItems
          items={orderDetails.items}
          onClose={() => setShowSoldOutItems(false)}
        />
      )}

      <OrderItemsList
        items={orderDetails.items}
        cartItems={cart}
        onAdd={handleAddItem}
        onRemove={handleRemoveItem}
        showNoteFeature={showNoteFeature}
        orderNote={orderNote}
        onOpenNotePopup={showNoteFeature ? handleOpenNotePopup : undefined}
        onAddonsUpdate={onItemUpdate}
      />

      <AddMoreItems onClick={() => navigate(-1)} />

      {/* Delivery Options */}
      {orderDetails?.takeAwayEnabled && (
        <DeliveryOptions
          selectedOption={selectedFulfillmentType}
          onOptionChange={onFulfillmentChange}
        />
      )}

      {/* Coupon Section */}
      {showCoupon && (
        <div className="mx-3">
          {!appliedCoupon ? (
            <ApplyCouponCard onClick={onApplyCoupon} />
          ) : (
            <AppliedCouponCard
              coupon={appliedCoupon}
              onRemove={onRemoveCoupon}
            />
          )}
        </div>
      )}

      <PaymentDetails
        totalOrderAmount={orderDetails.totalOrderAmount}
        deliveryCharges={orderDetails.deliveryCharges}
        discountAmount={orderDetails.discountAmount}
        totalAmount={orderDetails.totalAmount}
        walletAmount={orderDetails.walletAmount}
        balancePayableAmount={orderDetails.balancePayableAmount}
        approxPricing={approxPricing}
        totalTaxAmt={orderDetails.totalTaxAmount}
        codOpted={orderDetails.codSelected}
        codAmount={orderDetails.codAmount}
        packagingCharges={orderDetails.packagingCharges}
        packagingTax={orderDetails.packagingTax}
        itemsTax={orderDetails.itemsTax}
        deliveryTax={orderDetails.deliveryTax}
      />

      {/* Note Popup */}
      {showNoteFeature && (
        <NotePopup
          visible={showNotePopup}
          initialNote={orderNote}
          onClose={handleCloseNotePopup}
          onSave={handleSaveNote}
          onClear={handleClearNote}
          cartKey={orderDetails.cartKey}
        />
      )}

      {/* Toast */}
      {showToast && (
        <Toast
          message={toastMessage}
          type={toastType}
          duration={2500}
          onClose={() => setShowToast(false)}
          position="bottom-center"
          showIcon
          showCloseButton={false}
          width="full"
          autoClose={true}
        />
      )}
    </div>
  );
};

export default SellerOrdersCard;
