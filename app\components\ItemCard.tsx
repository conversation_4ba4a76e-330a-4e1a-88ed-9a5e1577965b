import { ItemsVariantList } from "./ItemsVariantList";
import React, { useCallback, useState } from "react";
import { AvailableItem, Cart } from "~/types";
import Button from "./Button";
import ItemDetails from "./chooseitem/ItemDetails";
import AddItemButton from "./chooseitem/AddItemButton";
import GetBadge from "./chooseitem/GetBadge";
import BottomSheet from "./BottmSheet";
import VariantButton from "./chooseitem/VariantButton";
import CustomImage from "./CustomImage";
import SupplierBadge from "./common/SupplierBadge";

interface ItemCardProps {
  itemDetailsList: AvailableItem[];
  amount: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  cart: Cart;
}

const ItemCard: React.FC<ItemCardProps> = ({
  itemDetailsList,
  amount,
  cart,
  onAdd,
  onRemove,
  approxPricing
}) => {
  const [showItemDetails, setShowItemDetails] = useState(false);
  const [showVariants, setShowVariants] = useState(false);

  const sortedItems = useCallback(
    () =>
      [...itemDetailsList]
        .sort((a, b) => a.varSeq - b.varSeq)
        .sort((a, b) => {
          if ((a.soldout || a.closed) && !(b.soldout || b.closed)) return 1;
          if (!(a.soldout || a.closed) && (b.soldout || b.closed)) return -1;
          return a.varSeq - b.varSeq;
        }),
    [itemDetailsList]
  )();

  const itemDetails = useCallback(() => {
    const item = [...itemDetailsList]
      .sort((a, b) => a.varSeq - b.varSeq)
      .find((item) => !(item.soldout || item.closed));

    return item || itemDetailsList[0];
  }, [itemDetailsList])();

  const qty = useCallback(() => {
    return itemDetailsList.reduce(
      (acc, it) => acc + (cart[it.sellerItemId]?.qty || 0),
      0
    );
  }, [{ ...cart }])();

  const haslessdata = !(itemDetails.itemName && itemDetails.packaging);

  return (
    <div className="flex flex-col gap-2 relative bg-white rounded-md shadow-md  p-3 w-full">
      {/* Show Badges */}
      {itemDetails.closed || itemDetails.soldout ? (
        <div className="absolute inset-0 bg-white bg-opacity-60 rounded-lg">
          <div className="relative">
            <GetBadge itemDetails={itemDetails} />
          </div>
        </div>
      ) : (
        <GetBadge itemDetails={itemDetails} />
      )}
      {haslessdata && (
        <ItemInfo
          itemDetails={itemDetails}
          showItemDetails={showItemDetails}
          setShowItemDetails={setShowItemDetails}
          amount={amount}
          qty={qty}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          haslessdata={haslessdata}
          variantCount={itemDetailsList?.length || 0}
          setShowVariants={setShowVariants}
        />
      )}
      {!haslessdata && (
        <>
          <ItemInfo
            itemDetails={itemDetails}
            showItemDetails={showItemDetails}
            setShowItemDetails={setShowItemDetails}
            amount={amount}
            qty={qty}
            onAdd={onAdd}
            onRemove={onRemove}
            approxPricing={approxPricing}
            haslessdata={haslessdata}
            variantCount={itemDetailsList?.length || 0}
            setShowVariants={setShowVariants}
          />
          <div className="border-b border-neutral-300"></div>
          <div className="w-full self-end align-bottom">
            <PriceAndControls
              itemDetails={itemDetails}
              amount={amount}
              qty={qty}
              onAdd={onAdd}
              onRemove={onRemove}
              approxPricing={approxPricing}
              variantCount={itemDetailsList?.length || 0}
              setShowVariants={setShowVariants}
            />{" "}
          </div>
        </>
      )}

      <ItemsVariantList
        showVariants={showVariants}
        setShowVariants={setShowVariants}
        variants={sortedItems}
        cart={cart}
        onAdd={onAdd}
        onRemove={onRemove}
      />
      <BottomSheet
        isOpen={showItemDetails}
        onClose={() => setShowItemDetails(false)}
        className=" bg-gray-100 p-2"
      >
        <ItemDetails
          items={sortedItems}
          cart={cart}
          onAdd={onAdd}
          onRemove={onRemove}
          approxPricing={approxPricing}
          setShowItemDetails={setShowItemDetails}
        />
      </BottomSheet>
    </div>
  );
};

// Component for displaying item image, name, and badges
const ItemInfo: React.FC<{
  itemDetails: AvailableItem;
  showItemDetails: boolean;
  setShowItemDetails: (value: boolean) => void;
  amount: number;
  qty: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  haslessdata: boolean;
  variantCount: number;
  setShowVariants: (show: boolean) => void;
}> = ({
  itemDetails,
  showItemDetails,
  setShowItemDetails,
  amount,
  qty,
  onAdd,
  onRemove,
  approxPricing,
  haslessdata,
  variantCount,
  setShowVariants
}) => (
  <div className="flex flex-row items-start w-full gap-2">
    {/* <div
      className="flex w-full gap 4"
      // onClick={() => setShowItemDetails(!showItemDetails)}
    > */}
    <Button
      className={`transition-all duration-300 ease-in-out rounded-md aspect-square w-24 h-24`}
      onClick={() => setShowItemDetails(!showItemDetails)}
    >
      <CustomImage
        src={itemDetails.itemUrl}
        alt={""}
        className={`transition-all duration-300 ease-in-out rounded-md object-fill w-24 h-24`}
      />
    </Button>
    <div className="flex flex-grow flex-col justify-between pl-2 w-full ">
      <div className="flex flex-grow flex-col items-start text-start  w-full">
        <h2
          className="text-sm font-semibold text-typography-900 line-clamp-2"
          // onClick={() => setShowItemDetails(!showItemDetails)}
        >
          {itemDetails.itemName}
        </h2>
        <p
          className="text-xs text-typography-300 mt-1"
          // onClick={() => setShowItemDetails(!showItemDetails)}
        >
          {itemDetails.itemRegionalLanguageName}
        </p>
        {itemDetails.packaging ? (
          <p
            style={{ fontSize: "0.75rem" }}
            className="text-medium text--typography-500 mt-4"
            // onClick={() => setShowItemDetails(!showItemDetails)}
          >
            {itemDetails.packaging}
          </p>
        ) : null}
        {itemDetails.supplier !== "" ? (
          <div className="mt-4">
            <SupplierBadge supplier={itemDetails.supplier ?? ""} />
          </div>
        ) : null}
      </div>
      {haslessdata && (
        <div className="w-full self-end align-bottom">
          <PriceAndControls
            itemDetails={itemDetails}
            amount={amount}
            qty={qty}
            onAdd={onAdd}
            onRemove={onRemove}
            approxPricing={approxPricing}
            variantCount={variantCount}
            setShowVariants={setShowVariants}
          />
        </div>
      )}
    </div>
  </div>
);

// Component for displaying the price and quantity controls
const PriceAndControls: React.FC<{
  itemDetails: AvailableItem;
  amount: number;
  qty: number;
  onAdd: (item: AvailableItem) => void;
  onRemove: (item: AvailableItem) => void;
  approxPricing: boolean;
  variantCount: number;
  setShowVariants: (value: boolean) => void;
}> = ({
  itemDetails,
  amount,
  qty,
  onAdd,
  onRemove,
  approxPricing,
  variantCount,
  setShowVariants
}) => {
  const formatPrice = (price: number) =>
    price % 1 === 0 ? price.toFixed(0) : price.toFixed(2);

  return (
    <div className="flex flex-col w-full">
      <span className="h-4 self-end text text-xs font-light text-gray-700 ">
        {amount > 0 ? `₹ ${formatPrice(amount)}` : ""}
        {approxPricing && amount > 0 && (
          <span className="text-[10px] text-gray-400 self-center">
            (approx)
          </span>
        )}
      </span>

      <div className="flex justify-between items-center">
        <div className="flex items-center align-middle gap-1 flex-wrap">
          <span className="self-center text-xs text-gray-500">
            ₹
            <span className="text-xs font-medium text-gray-700">
              {itemDetails.pricePerUnit}
            </span>{" "}
            /{itemDetails.unit}
          </span>
          {itemDetails.discPerc > 0 && (
            <span className="text-[10px] font-light text-gray-400 line-through ml-2">
              ₹ {itemDetails.strikeoffPrice} /{itemDetails.unit}
            </span>
          )}
          {approxPricing && (
            <span className="text-[10px] text-gray-400 ml-1 self-end">
              (approx)
            </span>
          )}
        </div>

        {variantCount > 1 ? (
          <VariantButton
            qty={qty}
            variantCount={variantCount}
            isDisabled={itemDetails.closed || itemDetails.soldout}
            onClick={() => setShowVariants(true)}
          />
        ) : (
          <AddItemButton
            qty={qty}
            onAdd={() => {
              console.log("Add Item");
              onAdd(itemDetails);
            }}
            onRemove={() => onRemove(itemDetails)}
            isDisabled={itemDetails.closed || itemDetails.soldout}
            unit={itemDetails.unit}
          />
        )}
      </div>
    </div>
  );
};

export default ItemCard;
