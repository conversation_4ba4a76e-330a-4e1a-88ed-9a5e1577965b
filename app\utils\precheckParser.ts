import {
  PrecheckOrderResponse,
  Cart,
  CustomizationItem,
  AogList,
  AddonItem
} from "~/types";
import { generateCustomizationKey } from "./cartUtils";

// Extended AddonItem type for precheck response
export interface PrecheckAddonItem extends AddonItem {
  groupId?: number;
  groupName?: string;
}

/**
 * Converts PrecheckOrderResponse to Cart structure
 * @param precheckResponse - The precheck order response from the API
 * @returns Cart object with the new structure
 */
export const precheckResponseToCart = (
  precheckResponse: PrecheckOrderResponse
): Cart => {
  const cart: Cart = {};
  const itemCustomizations = new Map<number, CustomizationItem[]>();

  // First pass: Group items by sellerItemId and collect customizations
  precheckResponse.items.forEach((item) => {
    if (item.quantity <= 0) return;

    // Check if item has addons
    const hasAddons = item.addOnItemDtos && item.addOnItemDtos.length > 0;

    if (hasAddons) {
      // Process addon data
      const { flatAddons } = processAddonData(
        item.addOnItemDtos as PrecheckAddonItem[]
      );

      // Create customization item
      const customizationItem: CustomizationItem = {
        key: generateCustomizationKey(flatAddons, item.variationId),
        qty: item.quantity,
        amount: item.amount,
        flatAddons: flatAddons.length > 0 ? flatAddons : undefined,
        variationId: item.variationId
      };

      // Add to customizations map
      const existingCustomizations =
        itemCustomizations.get(item.sellerItemId) || [];
      itemCustomizations.set(item.sellerItemId, [
        ...existingCustomizations,
        customizationItem
      ]);
    } else {
      // For non-customizable items, create a cart item directly
      cart[item.sellerItemId] = {
        itemId: item.sellerItemId,
        qty: item.quantity,
        amount: item.amount,
        cartKey: precheckResponse.cartKey
      };
    }
  });

  // Second pass: Create cart items with grouped customizations
  itemCustomizations.forEach((customizations, sellerItemId) => {
    // Calculate total quantity and amount
    const totalQty = customizations.reduce((sum, cust) => sum + cust.qty, 0);
    const totalAmount = customizations.reduce(
      (sum, cust) => sum + cust.amount,
      0
    );

    cart[sellerItemId] = {
      itemId: sellerItemId,
      qty: totalQty,
      amount: totalAmount,
      cartKey: precheckResponse.cartKey,
      customizationList: customizations
    };
  });

  return cart;
};

/**
 * Process addon data from precheck response
 * @param addOnItemDtos - Array of addon items from precheck response
 * @returns Object containing flatAddons and aogList
 */
export const processAddonData = (addOnItemDtos: PrecheckAddonItem[]) => {
  const flatAddons: AddonItem[] = [];
  const aogMap = new Map<number, AogList>();

  addOnItemDtos.forEach((addon) => {
    if (addon.qty <= 0) return;

    // Add to flat addons list
    flatAddons.push({
      id: addon.id,
      sId: addon.sId,
      name: addon.name,
      price: addon.price,
      seq: addon.seq,
      qty: addon.qty,
      diet: addon.diet,
      aogId: addon.aogId
    });

    // Group addons by groupId
    if (addon.aogId) {
      if (!aogMap.has(addon.aogId)) {
        aogMap.set(addon.aogId, {
          id: addon.aogId,
          minSelect: 0, // These values should come from the API
          maxSelect: 0,
          name: addon.groupName || "Add-ons",
          description: "",
          seq: 0,
          addOnItemList: []
        });
      }

      const aog = aogMap.get(addon.aogId)!;
      aog.addOnItemList.push({
        id: addon.id,
        sId: addon.sId,
        name: addon.name,
        price: addon.price,
        seq: addon.seq,
        qty: addon.qty,
        diet: addon.diet,
        aogId: addon.aogId
      });
    }
  });

  return {
    flatAddons,
    aogList: Array.from(aogMap.values())
  };
};
