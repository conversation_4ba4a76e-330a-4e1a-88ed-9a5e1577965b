import { BottomAddressModel } from "./../components/address/BottomAddressModel";
import { LocationPermissionModal } from "~/components/address/LocationPermissionModal";
import { MapContainer } from "~/components/address/MapContainer";
import { AddressFormModal } from "~/components/address/AddressFormModal";
import Toast from "~/components/Toast";

// app/routes/changeaddress.tsx

import { useEffect, useState, useCallback, useRef } from "react";
import {
  Form,
  useActionData,
  useLoaderData,
  redirect,
  useNavigate,
  useLocation
} from "@remix-run/react";
import { ActionFunction, LoaderFunction, json } from "@remix-run/node";
import {
  getSession,
  commitSession,
  destroySession
} from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import { BackNavHeader } from "~/components/BackNavHeader";
import ErrorBoundaryComponent from "~/components/ErrorBoundary";
import { DecodedToken } from "~/types/user";
import { getAppSource } from "~/utils/loader";
import { addAddressAPI, updateAddressAPI } from "~/services/address.service";
import {
  AddressDto,
  AddressType,
  BuyerAddressDto
} from "~/types/address.types";
import { requireAuth } from "~/utils/clientReponse";
import { useRequireAuth } from "~/hooks/useRequireAuth";
interface LoaderData {
  buyerId: number;
  redirectTo?: string;
  addressId?: number;
  isEdit?: boolean;
  userName?: string;
}

interface ActionData {
  success?: boolean;
  message?: string;
  errors?: {
    [key: string]: string;
  };
  error?: string;
  redirectTo?: string;
}

interface LatLng {
  lat: number;
  lng: number;
}

interface LocationDetails {
  placeName: string;
  city: string;
  formattedAddress: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const url = new URL(request.url);

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: false, error: "Login required" });
  }

  if (!access_token) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${request.url}`, { headers });
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/login?redirectTo=${request.url}`, { headers });
    }

    const { buyerId, userName } = decoded.userDetails;
    const redirectTo = url.searchParams.get("redirectTo") || undefined;

    return {
      buyerId,
      redirectTo,
      userName
    };
  } catch (error) {
    console.error("Error decoding access_token:", error);
    return redirect("/login");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: false, error: "Login required" });
  }

  if (!access_token) {
    return redirect("/login");
  }

  const formData = await request.formData();
  const address = formData.get("address") as string;
  const autoCompleteAddress = formData.get("autoCompleteAddress");
  const nearbyLandmark = formData.get("nearbyLandmark");
  const latitude = formData.get("latitude") as string;
  const longitude = formData.get("longitude") as string;
  const redirectToPath = formData.get("redirectTo") as string;
  const addressId = formData.get("addressId") as string;
  const isEdit = formData.get("isEdit") === "true";
  const addressType = (formData.get("addressType") as AddressType) || "home";
  const customAddressLabel = formData.get("customAddressLabel") as string;
  const userName = formData.get("userName") as string;
  let redirectTo = undefined;

  // Simple validation
  const errors: { [key: string]: string } = {};
  if (!address) errors.address = "Address is required";
  if (!latitude) errors.latitude = "Latitude is required";
  if (!longitude) errors.longitude = "Longitude is required";
  if (addressType === "other" && !customAddressLabel.trim()) {
    errors.customLabel = "Please enter a label for the address type";
  }
  // Only validate userName if it's provided in the form
  if (formData.has("userName") && !userName?.trim()) {
    errors.userName = "Please enter your name";
  }

  // If address type is "other" and custom label is empty, use "Other" as fallback
  const finalAddressName =
    addressType === "other"
      ? customAddressLabel.trim() || "Other"
      : addressType;

  if (Object.keys(errors).length > 0) {
    return json<ActionData>({ errors }, { status: 400 });
  }

  try {
    const addressPayload: BuyerAddressDto = {
      name: finalAddressName,
      address: nearbyLandmark
        ? autoCompleteAddress
          ? address +
            ",\n" +
            autoCompleteAddress +
            ",\n Nearby Landmark: " +
            nearbyLandmark
          : address + ",\n Nearby Landmark: " + nearbyLandmark
        : autoCompleteAddress
        ? address + ",\n" + autoCompleteAddress
        : address,
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude)
    };

    let response;
    if (isEdit && addressId) {
      response = await updateAddressAPI(
        {
          addressId: parseInt(addressId),
          address: addressPayload,
          cName: userName || undefined
        },
        request
      );
    } else {
      response = await addAddressAPI(addressPayload, request);
    }

    // handle whatsapp redirect
    const appSource = getAppSource(request);

    if (redirectToPath) {
      redirectTo = redirectToPath;
    } else if (appSource === "whatsappchat") {
      redirectTo = "/chooseitems";
    } else {
      redirectTo = "/home/<USER>";
    }

    if (!response.data.success) {
      return json<ActionData>(
        { success: false, error: response.data.error?.message },
        { status: 500 }
      );
    }

    // Return success response with headers
    return json(
      { success: true, redirectTo },
      {
        headers: response.headers?.has("Set-Cookie")
          ? {
              "Set-Cookie": response.headers.get("Set-Cookie")!
            }
          : undefined
      }
    );
  } catch (error: unknown) {
    console.error("Error updating address:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Failed to update address";
    return json<ActionData>(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
};

export default function ChangeAddress() {
  useRequireAuth();

  const actionData = useActionData<ActionData>();
  const { redirectTo, userName } = useLoaderData<LoaderData>();
  const location = useLocation();
  const navigate = useNavigate();
  const addressFromState = location.state?.address as AddressDto | undefined;
  const [addressDto] = useState<AddressDto | undefined>(addressFromState);
  const [isEdit] = useState(location.state?.isEdit as boolean | undefined);
  const [showErrorToast, setShowErrorToast] = useState(false);
  const [showPermissionErrorToast, setShowPermissionErrorToast] =
    useState(false);
  const [permissionErrorMessage, setPermissionErrorMessage] = useState("");
  const [currentUserName, setCurrentUserName] = useState(userName || "");

  // Check if addressFromState exists and has a name
  const addressNameFromState = addressFromState?.name;
  const isStandardType =
    addressNameFromState === "home" ||
    addressNameFromState === "work" ||
    addressNameFromState === "other";

  const [addressType, setAddressType] = useState<AddressType>(
    isEdit
      ? isStandardType
        ? (addressNameFromState as AddressType)
        : "other"
      : "home"
  );

  const [customAddressLabel, setCustomAddressLabel] = useState<string>(
    !isStandardType && typeof addressNameFromState === "string"
      ? addressNameFromState
      : ""
  );

  const [address, setAddress] = useState(
    addressFromState
      ? addressFromState.address?.includes(",\n")
        ? addressFromState.address?.split(",\n", 1)[0]
        : addressFromState?.address
      : ""
  );
  const [nearbyLandmarkaddress, setNearbyLandmarkaddress] = useState(
    addressFromState?.address?.includes(",\n Nearby Landmark: ")
      ? addressFromState.address?.split(",\n Nearby Landmark: ")[
          addressFromState.address?.split(",\n Nearby Landmark: ").length - 1
        ]
      : ""
  );
  const [latitude, setLatitude] = useState(addressFromState?.latitude || "");
  const [longitude, setLongitude] = useState(addressFromState?.longitude || "");
  const [saveAddressModelClicked, setSaveAddressModelClicked] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const [autoCompleteAddress, setAutoCompleteAddress] = useState("");
  const [locationPermissionStatus, setLocationPermissionStatus] = useState<
    "granted" | "denied" | "prompt" | "unknown"
  >("unknown");
  const [showLocationPermissionModal, setShowLocationPermissionModal] =
    useState(false);
  const [locationDetails, setLocationDetails] = useState<LocationDetails>({
    placeName: "",
    city: "",
    formattedAddress: ""
  });
  const [isLoadingCurrentLocation, setIsLoadingCurrentLocation] =
    useState(false);

  // Reference to track whether map has been initialized
  const mapInitialized = useRef(false);

  // Calculate if name field should be disabled
  const showNameField: boolean =
    isEdit === true
      ? !userName ||
        parseInt(addressDto?.latitude || "0") === 0 ||
        parseInt(addressDto?.longitude || "0") === 0
      : !userName;

  // Check location permission status on component mount
  useEffect(() => {
    if (navigator.permissions) {
      navigator.permissions.query({ name: "geolocation" }).then((result) => {
        setLocationPermissionStatus(
          result.state as "granted" | "denied" | "prompt"
        );

        // Listen for changes to permission state
        result.onchange = () => {
          setLocationPermissionStatus(
            result.state as "granted" | "denied" | "prompt"
          );
        };
      });
    }
  }, []);

  // Create a stable version of this function using useCallback
  const getLocationName = useCallback((latLng: LatLng) => {
    console.log("Getting location name for coordinates:", latLng);
    if (!window.google?.maps) {
      console.error("Google Maps API not loaded");
      return;
    }

    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ location: latLng }, (results, status) => {
      if (status === "OK" && results && results[0]) {
        console.log("Geocode results:", results);

        const addressComponents = [
          ...results[0].address_components,
          ...(results[1]?.address_components || []),
          ...(results[2]?.address_components || [])
        ];

        const getComponent = (types: string[]) => {
          return addressComponents.find((comp) =>
            types.every((type) => comp.types.includes(type))
          );
        };

        const placeNameComponent =
          getComponent(["premise"]) ||
          getComponent(["subpremise"]) ||
          getComponent(["establishment"]) ||
          getComponent(["sublocality_level_4"]) ||
          getComponent(["sublocality_level_3"]) ||
          getComponent(["sublocality_level_2"]) ||
          getComponent(["sublocality_level_1"]) ||
          getComponent(["sublocality"]) ||
          getComponent(["locality"]);

        const cityComponent =
          getComponent(["locality"]) ||
          getComponent(["administrative_area_level_3"]) ||
          getComponent(["administrative_area_level_2"]) ||
          getComponent(["sublocality"]);

        const placeName = placeNameComponent
          ? placeNameComponent.long_name
          : "Unknown Place";
        const city = cityComponent ? cityComponent.long_name : "Unknown City";
        const formattedAddress = results[0]?.formatted_address;
        console.log("Location details:", { placeName, city, formattedAddress });

        setLocationDetails({ placeName, city, formattedAddress });
        setAutoCompleteAddress(formattedAddress);
      } else {
        console.error("Error fetching location name: ", status);
      }
    });
  }, []);

  const getCurrentLocation = useCallback(() => {
    console.log(
      "getCurrentLocation called, current status:",
      locationPermissionStatus
    );
    setIsLoadingCurrentLocation(true);

    if (locationPermissionStatus === "denied") {
      setShowLocationPermissionModal(true);
      setIsLoadingCurrentLocation(false);
      return;
    }

    // Ensure the geolocation API is available
    if (!navigator.geolocation) {
      console.error("Geolocation API not available");
      setIsLoadingCurrentLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log("Got current position:", position);
        const { latitude, longitude } = position.coords;

        // Update state with new coordinates - these will trigger the map marker to move
        setLatitude(latitude.toString());
        setLongitude(longitude.toString());

        // Get location name using coordinates
        getLocationName({ lat: latitude, lng: longitude });
        setIsLoadingCurrentLocation(false);

        // Track successful location fetch
        mapInitialized.current = true;

        console.log("Current location updated, map marker should move to:", {
          latitude,
          longitude
        });
      },
      (error) => {
        console.error("Error getting current location:", error);
        if (error.code === error.PERMISSION_DENIED) {
          setLocationPermissionStatus("denied");
          setShowLocationPermissionModal(true);
        }
        setIsLoadingCurrentLocation(false);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  }, [locationPermissionStatus, getLocationName]);

  useEffect(() => {
    if (actionData?.success && actionData.redirectTo) {
      navigate(actionData.redirectTo, {
        replace: true,
        state: { fromAddress: true }
      });
    }
  }, [actionData, navigate]);

  useEffect(() => {
    if (actionData?.success === false) {
      setShowErrorToast(true);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.success && actionData.redirectTo) {
      navigate(actionData.redirectTo, {
        replace: true,
        state: { fromAddress: true }
      });
    }
  }, [actionData, navigate]);

  // Monitor lat/lng changes to help with debugging
  useEffect(() => {
    if (latitude && longitude) {
      console.log("Main component lat/lng updated:", { latitude, longitude });

      // If coordinates are available, trigger location name lookup
      if (window.google?.maps && Number(latitude) && Number(longitude)) {
        getLocationName({
          lat: Number(latitude),
          lng: Number(longitude)
        });
      }
    }
  }, [latitude, longitude, getLocationName]);

  const handleLocationSelect = useCallback(
    (lat: string, lng: string, addressString: string) => {
      console.log("handleLocationSelect called with:", {
        lat,
        lng,
        addressString
      });
      setLatitude(lat);
      setLongitude(lng);
      setAutoCompleteAddress(addressString);
      getLocationName({ lat: Number(lat), lng: Number(lng) });
    },
    [getLocationName]
  );

  const handleBack = () => {
    // First check for returnTo in location state
    const stateReturnTo = location.state?.returnTo;

    if (stateReturnTo) {
      // Use replace instead of navigate to remove this page from history
      navigate(stateReturnTo, {
        replace: true,
        state: {
          ...(location.state || {}),
          fromAddress: true
        }
      });
    } else if (redirectTo) {
      // Use replace instead of navigate to remove this page from history
      navigate(redirectTo, {
        replace: true,
        state: {
          fromAddress: true
        }
      });
    } else {
      // Use replace instead of navigate to remove this page from history
      navigate("/home/<USER>", { replace: true });
    }
  };

  const handleOpenSettings = () => {
    // Instead of trying to open settings directly (which isn't possible in web apps),
    // we'll request the permission again which will prompt the user in their browser
    navigator.geolocation.getCurrentPosition(
      (position) => {
        // If successful, we can close the modal and proceed with getting location
        setShowLocationPermissionModal(false);
        const { latitude, longitude } = position.coords;
        setLatitude(latitude.toString());
        setLongitude(longitude.toString());
        getLocationName({ lat: latitude, lng: longitude });
        setIsLoadingCurrentLocation(false);
      },
      (error) => {
        // If still denied, just close the modal but keep the denied state
        console.error("Permission still denied:", error);
        setShowLocationPermissionModal(false);
        setIsLoadingCurrentLocation(false);

        // Show toast notification instead of alert
        setPermissionErrorMessage(
          "Location permission denied. Please enable location services."
        );
        setShowPermissionErrorToast(true);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  };

  const toggleAddressModal = () => {
    setSaveAddressModelClicked(!saveAddressModelClicked);
  };

  return (
    <div className="fixed inset-0 flex flex-col bg-white">
      {/* Header */}
      <BackNavHeader
        buttonText={
          isEdit ? "Edit delivery location" : "Choose delivery location"
        }
        handleBack={handleBack}
      />

      {/* Permission Error Toast */}
      {showPermissionErrorToast && (
        <Toast
          type="error"
          message={permissionErrorMessage}
          duration={5000}
          onClose={() => setShowPermissionErrorToast(false)}
          position="bottom-center"
          width="full"
        />
      )}

      {/* Form Content */}
      <div className="flex-1 flex flex-col justify-center items-center h-full bg-white">
        <Form method="post" className="h-full w-full max-w-lg">
          {/* Map Container with Current Location Button */}
          <MapContainer
            googleMapsApiKey="AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY"
            latitude={latitude}
            longitude={longitude}
            onLocationSelect={handleLocationSelect}
            onCurrentLocationRequest={getCurrentLocation}
            isLoadingCurrentLocation={isLoadingCurrentLocation}
            searchFocused={searchFocused}
          />

          {/* Location Permission Modal */}
          <LocationPermissionModal
            isOpen={showLocationPermissionModal}
            onClose={() => setShowLocationPermissionModal(false)}
            onOpenSettings={handleOpenSettings}
          />

          {/* Address Form Modal */}
          <AddressFormModal
            isOpen={saveAddressModelClicked}
            onClose={toggleAddressModal}
            autoCompleteAddress={autoCompleteAddress}
            address={address}
            setAddress={setAddress}
            nearbyLandmarkAddress={nearbyLandmarkaddress}
            setNearbyLandmarkAddress={setNearbyLandmarkaddress}
            addressType={addressType}
            setAddressType={setAddressType}
            customAddressLabel={customAddressLabel}
            setCustomAddressLabel={setCustomAddressLabel}
            errors={actionData?.errors}
            showNameField={showNameField}
            isNameFieldDisabled={!showNameField}
            userName={currentUserName}
            setUserName={setCurrentUserName}
          />

          {/* Hidden Fields for form submission */}
          <input type="hidden" name="latitude" value={latitude} />
          <input type="hidden" name="longitude" value={longitude} />
          <input type="hidden" name="redirectTo" value={redirectTo} />
          <input type="hidden" name="addressType" value={addressType} />
          {showNameField && (
            <input type="hidden" name="userName" value={currentUserName} />
          )}
          <input
            type="hidden"
            name="autoCompleteAddress"
            value={autoCompleteAddress}
          />

          {addressType === "other" && (
            <input
              type="hidden"
              name="customAddressLabel"
              value={customAddressLabel}
            />
          )}
          <input
            type="hidden"
            name="redirectTo"
            value={location.state?.returnTo || redirectTo}
          />

          {isEdit && (
            <>
              <input
                type="hidden"
                name="addressId"
                value={addressDto?.addressId}
              />
              <input type="hidden" name="isEdit" value="true" />
            </>
          )}

          {/* Error Message */}
          {actionData?.message && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
              {actionData.message}
            </div>
          )}
        </Form>
      </div>

      {/* Bottom Sheet */}
      <BottomAddressModel
        setSaveAddressModelClicked={setSaveAddressModelClicked}
        setSearchFocused={setSearchFocused}
        saveAddressModelClicked={saveAddressModelClicked}
        locationDetails={locationDetails}
        showErrorToast={showErrorToast}
        setShowErrorToast={setShowErrorToast}
        actionData={actionData}
        addressType={addressType}
        setAddressType={setAddressType}
        customAddressLabel={customAddressLabel}
        setCustomAddressLabel={setCustomAddressLabel}
      />
    </div>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onClose={() => navigate(-1)} />;
}
